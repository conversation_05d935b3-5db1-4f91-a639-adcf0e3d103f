.base: &base
  only:
    - main
    - master
    - develop
#    - /^feat(ure)?\/.*$/
#    - /^release\/.*$/

stages:
  - sast
  - ship
#  - deploy

project-lint:
  <<: *base
  stage: sast
  allow_failure: false
  image: ${DOCKER_REGISTRY}/rk-infrav2/project-linter
  script:
    - export LINT_DESC=off
    - export LINT_NAME=on
    - export LINT_VERSION=on
    - /go/bin/project-linter

docker-env-lint:
  <<: *base
  stage: sast
  allow_failure: false
  image: ${DOCKER_REGISTRY}/docker.io/library/alpine:3.12
  script:
    - grep -w ".git" .dockerignore || echo ".git" >> .dockerignore

ship:
  <<: *base
  stage: ship
  image: hub-dev.rockontrol.com/docker.io/morlay/buildx:c04344f
  before_script:
    - echo "${DOCKER_PASSWORD}" | docker login "${DOCKER_REGISTRY}" -u="${DOCKER_USERNAME}" --password-stdin
    - hx buildx ci-setup
    #- BUILDKIT_NAME="hx buildx ci-setup"
  script:
    # - make build
    - export PLATFORM=linux/amd64,linux/arm64
    - source .platform || echo "WARNING, there is no .platform in project, USE default PLATFORM=${PLATFORM} "
    #- hx buildx --with-builder --push
    - hx buildx --with-builder --push --platform="${PLATFORM}" --local-cache=/runner-cache/"${CI_PROJECT_NAME}"/.buildx

#deploy:
# <<: *base
# stage: deploy
# script:
#   - hx deploy
# dependencies:
#   - ship
