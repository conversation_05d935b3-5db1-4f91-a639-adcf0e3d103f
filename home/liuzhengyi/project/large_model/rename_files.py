import os
import re

def rename_files_in_directory(directory_path):
    """
    重命名目录中的文件，去掉文件名中下划线及之前的部分
    """
    # 获取目录中的所有文件
    try:
        files = os.listdir(directory_path)
        renamed_count = 0
        
        for filename in files:
            # 检查文件名是否包含下划线
            if '_' in filename:
                # 分离文件名和扩展名
                name, ext = os.path.splitext(filename)
                
                # 找到最后一個下划线的位置
                last_underscore_index = name.rfind('_')
                
                # 如果找到了下划线，则去掉下划线及之前的部分
                if last_underscore_index >= 0:
                    new_name = name[last_underscore_index + 1:] + ext
                    
                    # 构造完整的文件路径
                    old_file_path = os.path.join(directory_path, filename)
                    new_file_path = os.path.join(directory_path, new_name)
                    
                    # 重命名文件
                    try:
                        os.rename(old_file_path, new_file_path)
                        print(f"重命名成功: {filename} -> {new_name}")
                        renamed_count += 1
                    except Exception as e:
                        print(f"重命名失败 {filename}: {str(e)}")
            
        print(f"\n总共重命名了 {renamed_count} 个文件")
        
    except Exception as e:
        print(f"访问目录时出错: {str(e)}")

if __name__ == "__main__":
    directory_path = "/home/<USER>/project/alg/photo"
    print(f"开始处理目录: {directory_path}")
    rename_files_in_directory(directory_path)
    print("处理完成")