# 城市校验错误响应示例

## 1. 无效城市参数错误 (400)

**请求**: `GET /permit-eia/oil-quantity?city=无效城市`

**响应**:
```json
{
  "detail": {
    "error": "城市参数无效",
    "invalid_city": "无效城市",
    "supported_cities": [
      "北京市",
      "上海市", 
      "九龙坡区",
      "太原市",
      "呼和浩特市",
      "天津市",
      "东莞市",
      "萍乡市",
      "营口市",
      "广州市",
      "西宁市",
      "宁波市",
      "重庆市",
      "成都市",
      "深圳市",
      "杭州市",
      "南京市",
      "武汉市",
      "西安市",
      "郑州市"
    ],
    "total_supported": 50,
    "message": "不支持的城市: '无效城市'，请从支持的城市列表中选择",
    "note": "更多支持的城市请调用 GET /cities 接口查询"
  }
}
```

## 2. 空城市参数错误 (400)

**请求**: `GET /permit-eia/oil-quantity?city=`

**响应**:
```json
{
  "detail": "城市参数不能为空"
}
```

## 3. 数据库服务不可用错误 (500)

**请求**: `GET /permit-eia/oil-quantity?city=北京市` (当数据库不可用时)

**响应**:
```json
{
  "detail": {
    "error": "城市数据服务不可用",
    "message": "无法获取支持的城市列表，请联系管理员检查数据库连接",
    "invalid_city": "北京市"
  }
}
```

## 4. 获取城市列表成功 (200)

**请求**: `GET /cities`

**响应**:
```json
{
  "status": "OK",
  "data": [
    "北京市",
    "上海市",
    "九龙坡区",
    "太原市",
    "呼和浩特市",
    "天津市",
    "东莞市",
    "萍乡市",
    "营口市",
    "广州市",
    "西宁市",
    "宁波市"
  ],
  "count": 12,
  "cache_info": {
    "city_count": 12,
    "last_updated": "2025-08-21T10:30:00.123456",
    "cache_expired": false,
    "cache_hours": 24,
    "performance": {
      "cache_hits": 150,
      "cache_misses": 5,
      "hit_rate_percent": 96.77,
      "total_requests": 155
    }
  }
}
```

## 5. 获取城市列表失败 (500)

**请求**: `GET /cities` (当数据库不可用时)

**响应**:
```json
{
  "detail": {
    "error": "城市数据服务不可用",
    "message": "无法获取支持的城市列表，请联系管理员检查数据库连接",
    "cache_info": {
      "city_count": 0,
      "last_updated": null,
      "cache_expired": true,
      "cache_hours": 24,
      "performance": {
        "cache_hits": 0,
        "cache_misses": 1,
        "hit_rate_percent": 0.0,
        "total_requests": 1
      }
    }
  }
}
```


