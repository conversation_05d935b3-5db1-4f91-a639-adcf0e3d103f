"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-17 10:37:11 AM

  @ FilePath:  -> srv-online-doc-chunk-and-highlight -> mod -> parse_doc.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-

import os
import sys
import base64

from typing import Dict, Any
from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.text.paragraph import Paragraph
from docx.table import Table


class PARSE_DOCX_TO_MARKDOWN:
    def __init__(self, docx_path, output_path = None):
        self.docx_path = docx_path
        self.output_path = output_path
        self.doc = Document(docx_path)

    def iter_block_items(self):
        """按顺序遍历文档中的段落和表格"""
        for child in self.doc.element.body.iterchildren():
            if isinstance(child, CT_P):
                yield Paragraph(child, self.doc)
            elif isinstance(child, CT_Tbl):
                yield Table(child, self.doc)
    
    @staticmethod
    def table_title(table: Table):
        """获取表格标题"""
        try:
            cnt = 0
            previous = table._element.getprevious()
            while cnt < 3:
                if previous is not None and previous.text.strip() != '':
                    return previous.text
                else:
                    previous = previous.getprevious()
                cnt += 1
            return ''
        except:
            return ''


    @staticmethod
    def process_paragraph(paragraph: Paragraph, keep_style: bool = False):
        """处理段落，包含文本样式和图片"""
        parts = []

        for run in paragraph.runs:
            text = run.text
            # 处理粗体、斜体、下划线
            if keep_style:
                if run.bold: text = f'<strong>{text}</strong>'
                if run.italic: text = f'<em>{text}</em>'
                if run.underline: text = f'<u>{text}</u>'
            
            # 处理超链接
            if paragraph.hyperlinks:
                rels = paragraph.part.rels
                link_id = run.hyperlink.id
                if link_id in rels:
                    link_target = rels[link_id]._target
                    text = f'<a href="{link_target}">{text}</a>'
            
            parts.append(text)
            
            if run._element.xpath(".//pic:pic"):
                tmp = run._element.xpath(".//pic:pic")[0]
                # 获取图片的rId
                rId = tmp.blipFill.blip.embed
                image = run.part.rels[rId].target_part
                image_bytes = image.blob
                image_base64 = base64.b64encode(image_bytes).decode()
                image_type = image.content_type.split('/')[-1]
                # 图片居中显示
                if keep_style:
                    parts.append(f'<div style="text-align: center;"><img src="data:image/{image_type};base64,{image_base64}"/></div>')
                else:
                    parts.append(f'![image](data:image/{image_type};base64,{image_base64})')
        
        content = ''.join(parts).strip()
        if not content: return ''

        # 处理标题样式
        style = paragraph.style.name.lower()
        if style.startswith('heading'):
            level_str = ''.join(filter(str.isdigit, style))
            level_num = int(level_str) if level_str else 1
            level = min(level_num, 6)
            return f'{"#" * level} {content}\n'
        
        return f'{content}\n'

    @staticmethod
    def process_table(table: Table):
        """处理表格，支持colspan, rowspan"""

        # 获取表格的行数和列数
        html_rows = []
        # 记录已经使用过的单元格
        index_no_use = set() 
        for irow, row in enumerate(table.rows):

            html_cells = ""
            for icol, cell in enumerate(row.cells):
                
                if f'{irow}_{icol}' in index_no_use:
                    continue

                # 获取colspan
                colspan = cell._tc.grid_span if hasattr(cell._tc, "grid_span") else 1

                if colspan > 1 and f'{irow}_{icol}' not in index_no_use:
                    for i in range(icol, icol+colspan):
                        # index_no_use.add(f'{irow}_{i}')
                        index_no_use.update(set(f'{irow_}_{_}' for _ in range(icol, icol + colspan)))

                # 获取rowspan, 需要考虑vMerge是continue还是restart的情况, 目前由于没有contnue, 所以判断只要vMerge为restart, 就继续往下找
                rowspan = 1    
                if hasattr(cell._tc, "vMerge"):
                    v_memrge = cell._tc.vMerge
                    if v_memrge:
                        for irow_ in range(irow+1, len(table.rows)):
                            # print(cell.text, table.rows[irow_].cells[icol].text, v_memrge)
                            if table.rows[irow_].cells[icol].text == cell.text and v_memrge in ['restart', 'continue']:
                                rowspan += 1
                                # index_no_use.add(f'{irow_}_{icol}')
                                index_no_use.update(set(f'{irow_}_{_}' for _ in range(icol, icol + colspan)))
                            else:
                                break
                
                if colspan > 1:      
                    if rowspan > 1: 
                        html_cells = f'<td rowspan="{rowspan}" colspan="{colspan}">' 
                    else:
                        html_cells += f'<td colspan="{colspan}">'
                else:
                    if rowspan > 1:
                        html_cells += f'<td rowspan="{rowspan}">'
                    else:
                        html_cells += f'<td>'
                
                # 获取内容
                html_cells = html_cells + ''.join(p.text for p in cell.paragraphs) + '</td>\n'

            html_rows.append(f'<tr>\n{html_cells}</tr>')


        return '<table>\n{}\n</table>'.format('\n'.join(html_rows))

    def docx_to_markdown(self, keep_style: bool = False):
        """主转换函数"""
        output = []
        
        for block in self.iter_block_items():
            if isinstance(block, Paragraph):
                md = self.process_paragraph(block, keep_style)
                if md: output.append(md)
            elif isinstance(block, Table):
                html = self.process_table(block)
                if html: output.append(html)
        
        if self.output_path is not None:
            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            with open(self.output_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(output))
        else:
            return '\n\n'.join(output)


if __name__ == '__main__':  
    # 使用示例
    from lib import proj_dir
    docx_path = f'{proj_dir}/data/95.重庆登科金属制品-自行监测方案.docx'
    output_path = f'{proj_dir}/data/output.md'
    parser = PARSE_DOCX_TO_MARKDOWN(docx_path, output_path)
    parser.docx_to_markdown(keep_style=False)
