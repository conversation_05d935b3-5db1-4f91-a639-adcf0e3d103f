"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-21 4:07:10 PM



  @ FilePath:  -> srv-parse-pollutant-discharge-permit -> lib -> step_2_extract_polluant_discharge_permit_bed.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-


import os
import re
import sys
import logging
import requests
import pandas as pd

from typing import Union, List
from docx import Document
from docx.oxml import parse_xml
from docx.table import Table
from thefuzz import process
from multiprocessing import Pool, cpu_count

sys.path.append('../')

from lib.extract_from_file import logger, proj_dir, progress_bar
from lib.extract_from_file.file_parser.parse_docx import PARSE_DOCX_TO_MARKDOWN


class DocumetParser(object):

    def __init__(self, file_path: str, logger: logging.Logger = logger):
        self.file_path = file_path
        self.doc = Document(file_path)
        self.logger = logger or logging.getLogger(__name__)

    def find_table_by_name(self, table_indicator: str) -> Union[Table, None]:
        """根据表格名称查找表格"""
        for table in self.doc.tables:
            if table_indicator in self.get_table_name(table):
                return table
        else:
            self.logger.error("表格不存在")
            return None
    
    def get_table_name(self, table: Table = None) -> Union[str, list]:
        """获取表格名称"""
        if table is not None:
            return PARSE_DOCX_TO_MARKDOWN.table_title(table)
        table_name = []
        for table in self.doc.tables:
            # 假设标题在表格前面的一段文字中, 将文字和文字所在章节的表头拼接起来作为表名
            if table._element.getprevious() is not None:
                table_name.append(PARSE_DOCX_TO_MARKDOWN.table_title(table))
        return table_name

    @staticmethod
    def _table_to_csv(table: Table):
        rows = []
        for row in table.rows:
            rows.append('|'.join([cell.text for cell in row.cells]))
        return '\n\n'.join(rows)

    @staticmethod
    def get_table_index_by_name(table: Table, table_indicator: str, 
                                col_select: bool = True, 
                                next_indicator: str = None):
        """根据表格内容查找表格索引"""

        data = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                if not cell.tables:
                    cell.text = cell.text.replace('\n', '').replace(' ', '').replace('\t', '')
                    row_data.append(cell.text)
                    continue
                # 每个cell只处理一层table
                row_data.append(DocumetParser._table_to_csv(cell.tables[0]))
            data.append(row_data)

        data = pd.DataFrame(data)
        tmp_ind_x = data.index[data.isin([table_indicator]).any(axis=1)]
        if len(tmp_ind_x) == 0:
            return 'NotFound'
        ind_x = tmp_ind_x[-1]
        if next_indicator is not None:
            ind_x_end = data.index[data.isin([next_indicator]).any(axis=1)][0]
            return _drop_duplicate(data.iloc[ind_x + 1:ind_x_end, :])

        tmp_ind_y = data.columns[data.isin([table_indicator]).any(axis=0)]
        if len(tmp_ind_x) == 0:
            return 'NotFound'
        ind_y = tmp_ind_y[0]
        # 只返回下一个索引对应的值
        
        if col_select: 
            for i in range(ind_y + 1, data.shape[1]):
                if data.iloc[ind_x, i] != table_indicator:
                    return data.iloc[ind_x, i]    
            else:
                return 'NotFound'
        else:
            for i in range(ind_x + 1, data.shape[0]):
                if data.iloc[i, ind_y] != table_indicator:
                    return data.iloc[i, ind_y]
            else:
                return 'NotFound'

    @staticmethod
    def _fuzzy_match(name, dict_name, threshold=50):
        """模糊匹配企业名称"""

        match = process.extractOne(name, list(dict_name.keys()))
        if match[1] >= threshold:
            return dict_name[match[0]]
        else:
            logger.warning(f'模糊匹配企业名称失败: {name}')
            return ''


def _drop_duplicate(tmp_pd: pd.DataFrame):
    tmp_ = tmp_pd.columns[tmp_pd.T.duplicated()]
    return tmp_pd.drop(columns=tmp_).reset_index(drop=True)

def process_bed_number(file, city_name):
    try:
        doc = DocumetParser(file)
        table = doc.find_table_by_name('排污单位基本信息表')
        bed_number = doc.get_table_index_by_name(table, '病床数', col_select=True)
        if bed_number == 'NotFound':
            logger.info(f'{city_name} -> {file} -> 解析失败 -> 没有查询到病床数')
            return None
        firm_code = doc.get_table_index_by_name(table, '统一社会信用代码', col_select=True)
        firm_name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
        return {'统一社会信用代码': firm_code, '单位名称': firm_name, '病床数': bed_number}
    except Exception as e:
        logger.error(f'{city_name} -> {file} -> 解析失败 -> {e}')
        return None

def get_ents_bed_number(file_dir: list, dir_out: str, city_name: str):
    os.makedirs(dir_out, exist_ok=True)
    bed_info = pd.DataFrame()
    results = []
    bar = progress_bar(len(file_dir), desc='床位解析进度')
    def bar_update(*args, **kwargs):
        bar.update()

    with Pool(max_processors()) as pool:
        for file in file_dir:
            results.append(pool.apply_async(process_bed_number, args=(file, city_name,), callback=bar_update))
        results = [res.get() for res in results if res.get() is not None]
    if results:
        bed_info = pd.DataFrame(results)
        bed_info.to_csv(f'{dir_out}/{city_name}_病床数.csv', index=False)
        logger.info(f'{city_name} -> 病床数解析成功')
    else:
        logger.info(f'{city_name} -> 病床数解析失败 -> 没有有效数据')


def get_all_files(file_dir: str):
    # 获取所有docx文件
    file_out = []
    for root, _, files in os.walk(file_dir):
        for file in files:
            if not file.endswith('.docx'):
                continue
            file_out.append(f'{root}/{file}')
    return file_out


def max_processors(max_cpu_count=3):
    return min(max_cpu_count, cpu_count())


if __name__ == "__main__":
    # dirs = '/mnt/c/Users/<USER>/Desktop/九龙坡项目/排污许可证/九龙坡230家排污许可证-doc/'
    logger = logging.getLogger(__name__)
    logger.handlers = [logging.FileHandler(f'{proj_dir}/data/logs/step_2_extract_polluant_discharge_permit_bed.log')]
    logger.setLevel(logging.INFO)

    dirs_dict = dict(zip(['上海市', '北京市', '太原市', '呼和浩特市', '天津市'], 
        ['/mnt/e/pol/排污许可证-docx/上海市', 
        '/mnt/e/pol/排污许可证-docx/北京市',  
        '/mnt/e/pol/排污许可证-docx/山西省',
        '/mnt/e/pol/排污许可证-docx/内蒙古自治区',
        '/mnt/e/pol/排污许可证-docx/天津市']
    ))
    for city_name, dirs_in in dirs_dict.items():
        # city_name = '上海市'
        # dirs_in = '/mnt/e/pol/排污许可证-docx/上海市'
        files_in = get_all_files(dirs_in)
        dirs_out=f'{proj_dir}/data/parsed/{city_name}/'

        # 获取病床数
        get_ents_bed_number(files_in, dir_out=dirs_out, city_name=city_name)
