# -*- coding: utf-8 -*-
"""
@File  : extract_xining_data.py
@Author: <PERSON>
@Date  : 2025/6/4 9:09
@Desc  : 
"""
import os
import pandas as pd
from docx import Document
from tqdm import tqdm

from lib import proj_dir
from lib.extract_from_file.step_2_extract_polluant_discharge_permit_bed import DocumetParser


def row_next_cell(tbl, i, j):
    """ 计算tbl表格（i，j）单元格的下一个行内容不同的单元格 """
    row = tbl.rows[i]
    for curr_j, cell in enumerate(row.cells[j:], j):
        if curr_j == j:
            aim_text = cell.text
            continue
        if cell.text != aim_text:
            return cell.text
    return ''


def col_next_cell(tbl, i, j):
    """ 获取tbl表格（i，j）单元格的下一个列内容不同的单元格 """
    aim_text = tbl.rows[i].cells[j].text
    for curr_i, row in enumerate(tbl.rows[i:], i):
        cell_text = row.cells[j].text
        if cell_text != aim_text:
            return cell_text
    return ''


def drop_duplicate(lst):
    """ 删除列表中连续重复的内容，保持顺序不变 """
    if not lst:
        return lst
    curr = lst[0]
    res = [curr]
    for item in lst:
        if item != curr:
            res.append(item)
            curr = item
    return res


def clean_col_names(string):
    string = string.strip('\n')
    while '  ' in string:
        string = string.replace('  ', ' ')
    data = string.split('\t')
    data = [item.replace('\n', '') for item in data]
    return data


def rm_same_item(lst):
    """ 移除列表中所有相邻的重复元素 """
    res = []
    for item in lst:
        if not res or item != res[-1]:
            res.append(item)
    return res


def extract(base_path, ents:list):
    for ent in tqdm(ents):
        ent_path = os.path.join(base_path, ent)

        # 判断文件夹是否存在
        if not os.path.exists(ent_path):
            print(ent, '不存在')
            continue

        res = {}

        if os.path.isdir(ent_path):
            files_path = [
                file for file in os.listdir(ent_path)
                if not file.startswith('~$') and file.endswith('.docx')
            ]
            file_path = os.path.join(ent_path, files_path[0])
        else:
            file_path = ent_path
        print(file_path, '...')

        # if '湟水环境资源' not in file_path:
        #     continue

        doc = Document(file_path)
        tbls = doc.tables
        for tbl in tbls:
            title = tbl._element.getprevious().text

            # 表1  排污单位基本信息表
            if '排污单位基本信息' in title:
                tbl1 = {}
                row_lst = ['单位名称', '注册地址', '邮政编码', '生产经营场所地址', '行业类别', '投产日期',
                           '生产经营场所中心经度', '生产经营场所中心纬度', '组织机构代码', '统一社会信用代码', '技术负责人',
                           '联系电话', '所在地是否属于大气重点控制区', '所在地是否属于总磷控制区', '所在地是否属于总氮控制区',
                           '所在地是否属于重金属污染特别排放限值实施区域', '是否位于工业园区', '所属工业园区名称',
                           '是否需要改正', '排污许可证管理类别', '主要污染物类别', '大气污染物排放形式', '废水污染物排放规律',
                           '大气污染物排放执行标准名称', '水污染物排放执行标准名称']
                col_lst = ['危险废物经营许可证编号', '有效期限', '发证日期', '发证机关', '经营方式', '核准年经营规模（t/a）',
                           '核准利用规模（t/a）', '核准处置规模（t/a）']
                for i, row in enumerate(tbl.rows):
                    for j, cell in enumerate(row.cells):
                        if cell.text not in tbl1:
                            if cell.text in row_lst:
                                tbl1[cell.text] = row_next_cell(tbl, i, j)
                            elif cell.text in col_lst:
                                tbl1[cell.text] = col_next_cell(tbl, i, j)
                            elif cell.text == '主要污染物种类':
                                lst = [item.text for item in row.cells]
                                lst = drop_duplicate(lst)
                                tbl1[lst[0]] = '\n'.join(lst[1:])
                tbl1 = pd.DataFrame([list(tbl1.values())], columns=list(tbl1.keys()))
                res['排污单位基本信息表'] = tbl1
                break

        doc = DocumetParser(file_path)

        # 表17 主要产品及产能信息表
        tbl = doc.find_table_by_name('主要产品及产能信息表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            # cols = [
            #     '序号', '主要生产单元名称', '主要工艺名称（1）', '生产设施名称（2）', '生产设施编号', '参数名称', '设计值',
            #     '计量单位', '其他设施参数信息', '其他设施信息', '产品名称（4）', '生产能力（5）', '计量单位（6）',
            #     '设计年生产时间（h）（7）', '其他产品信息', '其他工艺信息'
            # ]
            cols = [_.text for _ in tbl.rows[1].cells]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['主要产品及产能信息表'] = data

        # 主要产品及产能信息补充表
        tbl = doc.find_table_by_name('主要产品及产能信息补充表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [_.text for _ in tbl.rows[1].cells]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['主要产品及产能信息补充表'] = data

        # 表18 主要原辅材料及燃料信息表
        tbl = doc.find_table_by_name('主要原辅材料及燃料信息表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '原料及辅料', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '种类（1）', '名称（2）', '年最大使用量计量单位（3）', '年最大使用量', '硫元素占比（%）',
                '有毒有害成分', '有毒有害成分及占比（%）（4）', '其他信息'
            ]
            try:
                data = pd.DataFrame(data, columns=cols).astype(str)
                res['主要原辅材料及燃料信息表'] = data
            except:
                pass

        # 表19  废气产排污节点、污染物及污染治理设施信息表
        tbl = doc.find_table_by_name('废气产排污节点、污染物及污染治理设施信息表')
        # header_column = [_.text for _ in  tbl.rows[1].cells]
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            # cols = [
            #     '序号', '产污设施编号', '产污设施名称（1）', '对应产污环节名称（2）', '污染物种类（3）', '排放形式（4）',
            #     '污染防治设施编号', '污染防治设施名称（5）', '污染防治设施工艺', '治理设施参数名称', '设计值', '计量单位',
            #     '是否为可行技术', '污染防治设施其他信息',
            #     '有组织排放口编号（6）', '有组织排放口名称', '排放口设置是否符合要求（7）', '排放口类型', '其他信息'
            # ]
            cols  = [_.text for _ in  tbl.rows[1].cells]

            data = pd.DataFrame(data, columns=cols).astype(str)
            res['废气产排污节点、污染物及污染治理设施信息表'] = data

        # 表20  废水类别、污染物及污染治理设施信息表
        tbl = doc.find_table_by_name('废水类别、污染物及污染治理设施信息表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            # cols = [
            #     '序号', '污染物种类（1）', '污染物名称（2）', '污染物类型（3）', '是否为可行技术（4）', '是否为可回收技术（5）',
            #     '是否为可降解技术（6）', '是否为可降解技术（7）', '是否为可降解技术（8）', '是否为可降解技术（9）',
            #     '是否为可降解技术（10）', '是否为可降解技术（11）', '是否]
            cols = [_.text for _ in tbl.rows[1].cells]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['废水类别、污染物及污染治理设施信息表'] = data

        # 表2  大气排放口基本情况表
        tbl = doc.find_table_by_name('大气排放口基本情况表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '排放口编号', '排放口名称', '污染物种类', '经度', '纬度', '排气筒高度（m）', '排气筒出口内径（m）（2）',
                '排气温度（℃）', '其他信息'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['大气排放口基本情况表'] = data

        # 表8 入河排污口信息表
        tbl = doc.find_table_by_name('入河排污口信息表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '排放口编号', '排放口名称', '入河排污口名称', '入河排污口编号', '入河排污口批复文号', '其他信息'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['入河排污口信息表'] = data

        # 表7  废水直接排放口基本情况表
        tbl = doc.find_table_by_name('废水直接排放口基本情况表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '排放口编号', '排放口名称', '排放口地理坐标（1）经度', '排放口地理坐标（1）纬度', '排放去向',
                '排放规律', '间歇排放时段', '受纳自然水体信息名称（2）', '受纳自然水体信息受纳水体功能目标（3）',
                '汇入受纳自然水体处地理坐标（4）经度', '汇入受纳自然水体处地理坐标（4）纬度', '其他信息'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['废水直接排放口基本情况表'] = data

        # 表3 大气污染物有组织排放
        tbl = doc.find_table_by_name('大气污染物有组织排放')
        if tbl is not None:
            # cols = [
            #     '序号', '排放口编号', '排放口名称', '污染物种类', '许可排放浓度限值', '许可排放速率限值(kg/h)',
            #     '许可年排放量限值（t/a）（第一年）', '许可年排放量限值（t/a）（第二年）', '许可年排放量限值（t/a）（第三年）',
            #     '许可年排放量限值（t/a）（第四年）', '许可年排放量限值（t/a）（第五年）', '承诺更加严格排放浓度限值'
            # ]
            cols = [_.text for _ in tbl.rows[1].cells]
            cols = rm_same_item(cols)
            data1 = doc.get_table_index_by_name(tbl, '主要排放口', col_select=False)
            data1 = [row.split('|') for row in data1.split('\n\n')]
            if data1 == [['']]:
                data1 = []
            data1 = pd.DataFrame(data1, columns=cols).astype(str)
            data2 = doc.get_table_index_by_name(tbl, '一般排放口', col_select=False)
            data2 = [row.split('|') for row in data2.split('\n\n')]
            if data2 == [['']]:
                data2 = []
            data2 = pd.DataFrame(data2, columns=cols).astype(str)
            res['有组织排放信息'] = pd.concat([data1, data2], ignore_index=True)

        # 表4 大气污染物无组织排放
        tbl  = doc.find_table_by_name('大气污染物无组织排放')
        if tbl is not None:
            # cols = [
            #     '序号', '生产设施编号/无组织排放编号', '产污环节', '污染物种类', '主要污染防治措施',
            #     '国家或地方污染物排放标准（名称）', '浓度限值', '其他信息', '许可年排放量限值（t/a）（第一年）',
            #     '许可年排放量限值（t/a）（第二年）', '许可年排放量限值（t/a）（第三年）', '许可年排放量限值（t/a）（第四年）',
            #     '许可年排放量限值（t/a）（第五年）', '申请特殊时段许可排放量限值'
            # ]
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            if data != [['']]:
                cols = [_.text for _ in tbl.rows[1].cells]
                data = pd.DataFrame(data, columns=cols).astype(str)
                res['大气污染物无组织排放表'] = data

        # 表4-1 水泥工业企业生产无组织排放控制要求
        tbl = doc.find_table_by_name('水泥工业企业生产无组织排放控制要求')
        if  tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '生产单元', '生产工序', '无组织排放控制要求', '公司无组织管控现状'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['水泥工业企业生产无组织排放控制要求'] = data

        # 表6  企业大气排放总许可量
        tbl = doc.find_table_by_name('企业大气排放总许可量')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '污染物种类', '第一年（t/a）', '第二年（t/a）', '第三年（t/a）', '第四年（t/a）', '第五年（t/a）'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['大气排放总许可量'] = data

        # 表7雨水排放口基本情况表
        tbl = doc.find_table_by_name('雨水排放口基本情况表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '排放口编号', '排放口名称', '排放口地理坐标（1）经度', '排放口地理坐标（1）纬度', '排放去向',
                '排放规律', '间歇排放时段', '受纳自然水体信息名称（2）', '受纳自然水体信息受纳水体功能目标（3）',
                '汇入受纳自然水体处地理坐标（4）经度', '汇入受纳自然水体处地理坐标（4）纬度', '其他信息'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['雨水排放口基本情况表'] = data

        # 表8  废水污染物排放
        tbl = doc.find_table_by_name('废水污染物排放')
        if tbl is not None:
            cols = [
                '序号', '排放口编号', '排放口名称', '污染物种类', '许可排放浓度限值', '许可年排放量限值（t/a）（第一年）',
                '许可年排放量限值（t/a）（第二年）', '许可年排放量限值（t/a）（第三年）', '许可年排放量限值（t/a）（第四年）',
                '许可年排放量限值（t/a）（第五年）'
            ]
            data1 = doc.get_table_index_by_name(tbl, '主要排放口', col_select=False)
            data1 = [row.split('|') for row in data1.split('\n\n')]
            if data1 == [['']]:
                data1 = []
            data1 = pd.DataFrame(data1, columns=cols).astype(str)
            data2 = doc.get_table_index_by_name(tbl, '一般排放口', col_select=False)
            data2 = [row.split('|') for row in data2.split('\n\n')]
            if data2 == [['']]:
                data2 = []
            data2 = pd.DataFrame(data2, columns=cols).astype(str)
            res['废水污染物排放'] = pd.concat([data1, data2], ignore_index=True)

            # 排放口合计
            cols = ['污染物种类', '许可年排放量限值（t/a）（第一年）', '许可年排放量限值（t/a）（第二年）',
                '许可年排放量限值（t/a）（第三年）', '许可年排放量限值（t/a）（第四年）', '许可年排放量限值（t/a）（第五年）']
            data3 = doc.get_table_index_by_name(tbl, '主要排放口合计', col_select=True)
            data3 = [row.split('|') for row in data3.split('\n\n')]
            data3 = pd.DataFrame(data3, columns=cols).astype(str)
            data3.insert(0, '排放口类型', '主要排放口合计')
            data4 = doc.get_table_index_by_name(tbl, '一般排放口合计', col_select=True)
            data4 = [row.split('|') for row in data4.split('\n\n')]
            data4 = pd.DataFrame(data4, columns=cols).astype(str)
            data4.insert(0, '排放口类型', '一般排放口合计')
            data5 = doc.get_table_index_by_name(tbl, '全厂排放口总计', col_select=True)
            data5 = [row.split('|') for row in data5.split('\n\n')]
            data5 = pd.DataFrame(data5, columns=cols).astype(str)
            data5.insert(0, '排放口类型', '全厂排放口总计')
            res['废水污染物排放合计'] = pd.concat([data3, data4, data5], ignore_index=True)

        # 表9 固体废物基础信息表
        tbl = doc.find_table_by_name('固体废物基础信息表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '固体废物类别', '固体废物名称', '代码', '危险特性', '类别', '物理性状', '产生环节', '去向', '备注'
            ]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['固体废物基础信息表'] = data

        # 表11 工业噪声排放信息表
        try:
            tbl = doc.find_table_by_name('工业噪声排放信息表')
            if tbl is not None:
                data1 = doc.get_table_index_by_name(tbl, '产噪单元编号', col_select=False)
                data1 = [row.split('|') for row in data1.split('\n\n')]
                cols = [
                    '产噪单元编号', '产噪单元名称', '主要产噪设施及数量', '主要噪声污染防治设施及数量'
                ]
                data1 = pd.DataFrame(data1, columns=cols).astype(str)

                tbl_sub = tbl.rows[2].cells[0].tables[0].rows[2].cells[0].tables[0]
                rows = []
                for row in tbl_sub.rows:
                    row_data = [cell.text for cell in row.cells]
                    rows.append(row_data)
                cols = ['排放标准名称及编号', '生产时段昼间', '生产时段夜间']
                data2 = pd.DataFrame(rows, columns=cols).astype(str)

                tbl_sub = tbl.rows[3].cells[0].tables[0].rows[4].cells[0].tables[0]
                rows = []
                for row in tbl_sub.rows:
                    row_data = [cell.text for cell in row.cells]
                    rows.append(row_data)
                cols = [
                    '厂界噪声点位名称', '厂界外声环境功能区类别', '工业噪声许可排放限值 dB(A)昼间等效声级',
                    '工业噪声许可排放限值 dB(A)夜间等效声级', '工业噪声许可排放限值 dB(A)夜间频发噪声最大声级',
                    '工业噪声许可排放限值 dB(A)夜间偶发噪声最大声级'
                ]
                data3 = pd.DataFrame(rows, columns=cols).astype(str)

                tbl_sub = tbl.rows[3].cells[0].tables[0].rows[6].cells[0].tables[0]
                rows = []
                for row in tbl_sub.rows:
                    row_data = [cell.text for cell in row.cells]
                    rows.append(row_data)
                cols = ['厂界噪声点位名称', '监测指标', '监测技术', '自动监测是否应联网', '手工监测频次']
                data4 = pd.DataFrame(rows, columns=cols).astype(str)
                res['工业噪声排放信息表'] = pd.concat([data1, data2, data3, data4], ignore_index=True, axis=0)
        except:
            pass

        # 表12 自行监测及记录表
        tbl = doc.find_table_by_name('自行监测及记录表')
        if tbl is not None:
            # cols = [
            #     '序号', '污染源类别/监测类别', '排放口编号/监测点位', '排放口名称/监测点位名称', '监测内容（1）',
            #     '污染物名称', '监测设施', '自动监测是否联网', '自动监测仪器名称', '自动监测设施安装位置',
            #     '自动监测设施是否符合安装、运行、维护等管理要求', '手工监测采样方法及个数（2）', '手工监测频次（3）',
            #     '手工测定方法（4）', '其他信息'
            # ]
            cols  = [_.text for _ in  tbl.rows[0].cells]
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            data = pd.DataFrame(data, columns=cols).astype(str)
            res['自行监测及记录表'] = data

        # 表13  环境管理台账记录表
        tbl = doc.find_table_by_name('环境管理台账记录表')
        if tbl is not None:
            data = doc.get_table_index_by_name(tbl, '序号', col_select=False)
            data = [row.split('|') for row in data.split('\n\n')]
            cols = [
                '序号', '类别', '记录内容', '记录频次', '记录形式', '其他信息'
            ]
            try:
                data = pd.DataFrame(data, columns=cols).astype(str)
                res['环境管理台账信息表'] = data
            except:
                pass

        ent = ent.replace('.docx', '')
        save_path = os.path.join(proj_dir, 'data', '许可证', '西宁许可证68家', f'{ent}.xlsx')
        with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
            # 将每个DataFrame写入不同的sheet
            for key, val in res.items():
                val.to_excel(writer, sheet_name=key, index=False)
    return


def xining_ents_path():
    base_path = '/home/<USER>/project/large_model/材料/西宁市68家'
    ents = [
        # "青海云天化国际化肥有限公司",
        # "青海亚洲硅业半导体有限公司",
        # "青海盐湖海纳化工有限公司（化工）",
        "黄河鑫业有限公司",
        # "青海天赐宏正环保科技有限公司二期"
    ]
    return base_path, ents


def jiulongpo_ents_path():
    base_path = '/home/<USER>/project/large_model/材料/九龙坡230家排污许可证-docx'
    files = [file for file in os.listdir(base_path) if not file.startswith('~$') and file.endswith('.docx')]
    return base_path, files


def xining68_ents_path():
    base_path = '/home/<USER>/project/large_model/材料/西宁市68家'
    ents = [folder for folder in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, folder))]
    return base_path, ents


def main():
    base_path, files = xining68_ents_path()
    extract(base_path, files)


if __name__ == '__main__':
    main()
