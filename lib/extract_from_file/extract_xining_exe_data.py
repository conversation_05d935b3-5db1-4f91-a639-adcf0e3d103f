# -*- coding: utf-8 -*-
"""
@File  : extract_xining_exe_data.py
@Author: <PERSON>
@Date  : 2025/6/6 16:43
@Desc  : 
"""
import json
import os
import pandas as pd
from tqdm import tqdm

from mod import DB
from lib import ori_database_conf, proj_dir


host = ori_database_conf['host']
port = ori_database_conf['port']
dbname = ori_database_conf['database']
user = ori_database_conf['user']
password = ori_database_conf['password']
db = DB(host, port, dbname, user, password)

ents = [
    "青海云天化国际化肥有限公司",
    "青海亚洲硅业半导体有限公司",
    "青海盐湖海纳化工有限公司（化工）",
    "黄河鑫业有限公司",
    "青海天赐宏正环保科技有限公司二期"
]

ents = ', '.join([f"'{ent}'" for ent in ents])
sql = """
    select f_enterprise_id, f_enterprise_name, f_report_type, f_report_time, f_report_content
    from t_pw_enterprise_zx_report t
    where t.f_enterprise_name in ({ents}) and t.f_report_type = '季报' 
"""
data = db.query_sql(sql.format(ents=ents))
data = data[data['f_report_time'].str[:4].astype(int)>=2024]

db.close()


data['f_report_time'] = data['f_report_time'].replace('第0', '第').replace('年0', '年')
for (ent_id, ent_name), group in tqdm(data.groupby(['f_enterprise_id', 'f_enterprise_name'])):
    for _, row in group.iterrows():
        res = {}
        report_time = row['f_report_time']
        content = row['f_report_content']
        try:
            content = json.loads(content)
        except:
            pass
        content = content['data']
        try:
            if isinstance(content, str):
                content = content.replace('\n', '').replace('\t', '')
            content = json.loads(content)
        except:
            pass

        assert content['overproofInfo']['tabTop'] == []
        if 'wasteGas' in content['overproofInfo']:
            overproof_air = content['overproofInfo']['wasteGas']
            overproof_air = pd.DataFrame(overproof_air)
            overproof_air['startTime'] = pd.to_datetime(overproof_air['startTime'], unit='s', utc=True) \
                                        .dt.tz_convert('Asia/Shanghai') \
                                        .dt.strftime('%Y-%m-%d %H:%M')
            overproof_air['endTime'] = pd.to_datetime(overproof_air['endTime'], unit='s', utc=True) \
                                        .dt.tz_convert('Asia/Shanghai') \
                                        .dt.strftime('%Y-%m-%d %H:%M')
            overproof_air['超标时段'] = overproof_air['startTime'] + ' - ' + overproof_air['endTime']

            over_map = {
                "productFacilityCode": "生产设施编号",
                "outletCode": "排放口编号",
                "pollutantName": "超标污染物种类",
                "emitValue": "实际排放浓度（折标）",
                "overproofReason": "超标原因说明"
            }
            overproof_air.rename(columns=over_map, inplace=True)
            overproof_air = overproof_air[['超标时段']+list(over_map.values())]
            res['有组织废气污染物超标时段小时均值报表'] = overproof_air

        if 'wasteGas' in content['exceptionInfo']:
            except_air = content['exceptionInfo']['wasteGas']
            except_air = pd.DataFrame(except_air)
            except_air['type'] = except_air['type'].map({'wasteGas': '废气防治设施'})

            except_map = {
                "type": "故障类型",
                "treatmentFacilityName": "故障设施",
                "reason": "故障原因",
                "pollutionFactor": "污染因子",
                "emitValue": "排放范围"
            }
            except_air.rename(columns=except_map, inplace=True)
            except_air = except_air[list(except_map.values())]
            except_air['超标时间（开始时段-结束时段）'] = ''
            res['废气污染设施异常情况汇总表'] = except_air

        if not content:
            continue
        try:
            content = content['emissionInfo']
        except:
            continue

        a_main = pd.DataFrame(content['airMainEmission'])
        a_minor = pd.DataFrame(content['airMinorEmission'])
        a_total = pd.DataFrame(content['airTotalEmission'])
        w_main = pd.DataFrame(content['waterMainEmission'])
        w_minor = pd.DataFrame(content['waterMinorEmission'])
        w_total = pd.DataFrame(content['waterTotalEmission'])

        a_main['type'] = 'main'
        a_minor['type'] = 'minor'
        a_total['type'] = 'total'
        w_main['type'] = 'main'
        w_minor['type'] = 'minor'
        w_total['type'] = 'total'

        air = pd.concat([a_main, a_minor, a_total], axis=0)
        water = pd.concat([w_main, w_minor, w_total], axis=0)

        cols_map = {
            "portType": "排放口类型",
            "outletCode": "排放口编号",
            "outletName": "排放口名称",
            "pollutantName": "污染物",
            "emitVolumnLimitYear": "许可排放量",
            "total": "季度合计实际排放量",
            "firstMonth": "第1个月实际排放量",
            "secondMonth": "第2个月实际排放量",
            "thirdMonth": "第3个月实际排放量",
            "remark": "备注",
            "emissionType": "排放方式"
        }
        air.rename(columns=cols_map, inplace=True)
        air = air[[col for col in cols_map.values() if col in air.columns]]
        res['废气实际排放量（季）'] = air
        water.rename(columns=cols_map, inplace=True)
        water = water[[col for col in cols_map.values() if col in water.columns]]
        res['废水实际排放量（季）'] = water

        over_water = pd.DataFrame(columns=[
            '超标时段', '排放口编号', '超标污染物种类', '实际排放浓度（折标）', '超标原因说明'
        ])
        res['废水污染物超标时段日均值报表'] = over_water

        save_path = os.path.join(proj_dir, 'data', '执行报告', '九龙坡区', f'{ent_name}-{report_time}.xlsx')
        with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
            # 将每个DataFrame写入不同的sheet
            for key, val in res.items():
                val.to_excel(writer, sheet_name=key, index=False)








