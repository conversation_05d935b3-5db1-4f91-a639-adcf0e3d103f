# -*- coding: utf-8 -*-
"""
@File  : extract_exe_docx_data.py
@Author: <PERSON>
@Date  : 2025/6/10 17:05
@Desc  : 
"""
import os
import pandas as pd
from docx import Document
import re
from tqdm import tqdm
from docx import Document
from docx.document import Document as DocumentObject
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph

from lib import proj_dir


def is_note_paragraph(paragraph_text: str) -> bool:
    """
    检查段落文本是否以“注”或常见数字列表格式开头。
    """
    text = paragraph_text.strip()
    # 匹配 "注"、"注："、"注1" 等
    if text.startswith('注'):
        return True

    # 匹配 "1."、"1、"、"(1)"、"①"、"1 " 等多种数字列表格式
    # \d+ 匹配一个或多个数字
    # [\.\s、)）] 匹配点、空格、顿号、英文括号、中文括号
    # [①②③④⑤⑥⑦⑧⑨⑩] 匹配中文序号
    if re.match(r'^(\d+[\.\s、)）]|[①②③④⑤⑥⑦⑧⑨⑩])', text):
        return True

    return False


def get_table_data(table: Table) -> list:
    """
    将表格的内容提取为一个二维列表（list of lists）。
    """
    table_data = []
    for row in table.rows:
        row_data = [cell.text.strip() for cell in row.cells]
        table_data.append(row_data)
    return table_data


def extract_tables_with_preceding_text(doc_path: str) -> list:
    """
    从Word文档中提取表格及其紧邻的标题段落和注释。

    Args:
        doc_path (str): Word文档的路径。

    Returns:
        list: 一个字典列表，每个字典包含：
              'title': 标题段落的文本。
              'notes': 注释段落的文本列表（按原始顺序）。
              'table_data': 二维列表形式的表格内容。
    """
    try:
        document = Document(doc_path)
    except Exception as e:
        print(f"打开或读取文件 '{doc_path}' 时出错: {e}")
        return []

    results = []
    # 获取文档body中的所有块级元素（段落和表格）
    body_elements = list(document.element.body)

    # 从后往前遍历，这样处理一个表格后，不会影响前面表格的索引
    for i in range(len(body_elements) - 1, -1, -1):
        element = body_elements[i]

        # 1. 找到一个表格
        if element.tag.endswith('tbl'):
            table = Table(element, document)

            preceding_paragraphs_texts = []
            title_paragraph_text = ""

            # 2. 从表格前一个元素开始，向前查找段落
            for j in range(i - 1, -1, -1):
                prev_element = body_elements[j]

                if not prev_element.tag.endswith('p'):
                    # 如果遇到非段落元素（例如另一个表格），则停止
                    break

                p = Paragraph(prev_element, document)
                p_text = p.text.strip()

                if not p_text:  # 跳过空段落
                    continue

                # 3. 判断段落是否为注释
                if is_note_paragraph(p_text):
                    preceding_paragraphs_texts.insert(0, p_text)  # 插入到列表开头以保持顺序
                else:
                    # 4. 如果不是注释，则认为是标题段落，停止查找
                    title_paragraph_text = p_text
                    break  # 找到标题，停止向前搜索

            # 5. 如果找到了标题段落，则处理并保存结果
            if title_paragraph_text:
                # 6. 提取表格内容
                table_data = get_table_data(table)

                # 7. 将结果保存在字典中
                results.insert(0, {
                    'title': title_paragraph_text,
                    'notes': preceding_paragraphs_texts,
                    'table_data': table_data
                })

    return results


def test():
    file_path = '/home/<USER>/project/large_model/材料/2022年重点企业执行报告/重庆泰濠制药有限公司/执行报告-重庆泰濠制药有限公司-2022年.docx'

    try:
        extracted_data = extract_tables_with_preceding_text(file_path)

        if extracted_data:
            print(f"成功从 '{file_path}' 中提取到 {len(extracted_data)} 个表格及其内容。\n")

            for i, data in enumerate(extracted_data, 1):
                print(f"--- 数据组 {i} ---")
                print(f"标题段落: {data['title']}")
                if data['notes']:
                    print("注释段落:")
                    for note in data['notes']:
                        print(f"  - {note}")
                else:
                    print("注释段落: (无)")

                print("表格内容:")
                # 打印表格内容的前5行作为预览
                for row in data['table_data'][:5]:
                    print(f"  {row}")
                if len(data['table_data']) > 5:
                    print("  ...")
                print("-" * 25 + "\n")

        else:
            print(f"在文档 '{file_path}' 中没有找到符合条件的表格和段落组合。")

    except FileNotFoundError:
        print(f"错误：文件 '{file_path}' 未找到。请检查文件路径是否正确。")
    except Exception as e:
        print(f"处理文档时发生未知错误: {e}")


def extract():
    base_path = '/home/<USER>/project/large_model/材料/2025年执行报告数据-九龙坡'
    save_path = os.path.join(proj_dir, 'data', '执行报告', '九龙坡-2025年')
    os.makedirs(save_path, exist_ok=True)
    for ent in tqdm(os.listdir(base_path)):
        ent_path = os.path.join(base_path, ent)
        if not os.path.isdir(ent_path):
            continue
        files = [file for file in os.listdir(ent_path) if file.endswith('.docx') and not file.startswith('~$')]
        for file in files:
            if file.startswith('convert_'):
                continue
            file_path = os.path.join(ent_path, file)
            res = extract_tables_with_preceding_text(file_path)
            dct = {}
            for item in res:
                key = item['title']
                data = item['table_data']
                while len(data) > 1 and data[0] == data[1]:
                    data.pop(0)
                data = pd.DataFrame(data[1:], columns=data[0])
                dct[key] = data

            full_path = os.path.join(save_path, file.replace('.docx', '.xlsx'))
            with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
                # 将每个DataFrame写入不同的sheet
                for key, val in dct.items():
                    key = key.replace('/', '_')
                    val.to_excel(writer, sheet_name=key, index=False)
    return


if __name__ == "__main__":
    # test()
    extract()