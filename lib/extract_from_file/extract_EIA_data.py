# -*- coding: utf-8 -*-
"""
@File  : extract_EIA_data.py
@Author: <PERSON>
@Date  : 2025/6/15 10:25
@Desc  : 
"""
import os
import re
import json
from collections import defaultdict
import pandas as pd
import shutil
from docx import Document
import warnings

from lib.llm.llm import ask_llm
from lib.llm import API_KEY
from lib.llm.rag_llm import (
    get_token,
    add_chat_model,
    add_embed_model,
    select_model,
    create_rag_obj,
    remove_assistant,
    Dataset,
    set_dialog,
    set_conversation,
    generate_conversation_id,
    chat
)
from lib.transition_docx_structure import transition_docx_structure, convert_docx_type
from lib import proj_dir
from lib.llm.rag_llm import upload_docs
from mod.load_and_save import save_json


def pipeline_docs(rag_obj, dataset_name, path):
    """ 创建数据集，将指定路径的所有文件加载并解析为chunk """
    datasets = Dataset(rag_obj)
    if dataset_name in datasets.list_datasets(only_name=True):
        datasets.delete(dataset_name)
    dataset = datasets.create_dataset(dataset_name)
    # for i in datasets.list_datasets():
    #     print(i)
    dataset = datasets.list_datasets(name=dataset_name)[0]
    _upload_docs(dataset, path)
    datasets.parse_dataset(dataset_name)
    for doc in dataset.list_documents():
        # if doc.run != 'DONE':
        print(doc)
    return dataset


def get_rag_assistant(file_path, file_type):
    # 生成auth, token, tenant_id
    auth, token, tenant_id = get_token()
    print('auth', auth)
    print('token', token)
    print('tenant_id', tenant_id)
    # 注册模型
    response = add_embed_model(auth)
    print(response)
    response = add_chat_model(auth, api_key=API_KEY)
    print(response)
    response = select_model(auth, tenant_id)
    print(response)
    rag_obj = create_rag_obj(api_key=token)
    dataset = pipeline_docs(rag_obj, file_type, file_path)
    print('dataset1', dataset.id)
    remove_assistant(rag_obj)
    return auth, dataset


def ask_rag(assistant, question, cache=False):
    """ 向rag(llm)提问，cache表示是否考虑历史对话记录 """
    def get_session():
        for session in assistant.list_sessions():
            if session.name == 'temp':
                if cache:
                   return session
                else:
                    assistant.delete_sessions(ids=[session.id])
                    return assistant.create_session('temp')
        return assistant.create_session('temp')

    session = get_session()
    full_ans = ''
    for ans in session.ask(question, stream=True):
        full_ans = ans.content
        # print(full_ans)
        # print('-' * 100)
    return full_ans


def ask_rag_full(auth, dataset_id, question):
    """ 向rag(llm)提问完整回复 """
    dialog_id = set_dialog(auth, dataset_id)
    conversation_id = generate_conversation_id()
    session = set_conversation(auth, dialog_id, conversation_id)
    ans = chat(auth, conversation_id, question)
    return ans


def extract_json_from_txt(txt):
    """ 从大模型回答中提取json结构 """
    pattern = r"```json(.*?)```"
    try:
        match = re.findall(pattern, txt, re.DOTALL)[-1]
        ans = json.loads(match)
    except:
        ans = None
    return ans


def convert_to_dict(obj):
    if isinstance(obj, defaultdict):  # 如果是 defaultdict 类型
        return {key: convert_to_dict(value) for key, value in obj.items()}
    elif isinstance(obj, list):  # 如果是列表类型，递归处理每个元素
        return [convert_to_dict(item) for item in obj]
    elif isinstance(obj, dict):  # 如果是普通 dict，递归处理每个键值对
        return {key: convert_to_dict(value) for key, value in obj.items()}
    else:  # 如果是其他类型，直接返回
        return obj


def add_space(s):
    # 匹配数字（包括小数）后接字母或汉字的情况
    pattern = r'(\d+\.?\d*)([A-Za-z\u4e00-\u9fff])'
    # 在数字和字母/汉字之间插入空格
    return re.sub(pattern, r'\1 \2', s)


def json_val_add_space(data):
    if isinstance(data, str):
        data = add_space(data)
    elif isinstance(data, list):
        for idx, item in enumerate(data):
            data[idx] = json_val_add_space(item)
    elif isinstance(data, dict):
        for key, val in data.items():
            data[key] = json_val_add_space(val)

    return data


def extract(file_path, file_name):
    auth, dataset = get_rag_assistant(file_path, '环评')
    ent_name = file_name
    ent_dataset = dataset

    def ask_many_times(question, times=3):
        for i in range(times):
            ans = ask_rag_full(auth, ent_dataset.id, question)
            ans = extract_json_from_txt(ans)
            if ans:
                return ans
        return None

    res = {}

    # 提取建设单位、编制时间、建设项目行业类别
    q0 = """
请根据环评报告"{file_name}"仔细分析回答`建设单位`和`编制日期`分别是什么。
最终结果请用json格式给出，并以```json开始，以```结束。若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。
仔细分析，谨慎输出"未找到（仅供参考）"。示例如下：
#### 结果输出示例1
```json
{{
    "建设单位": "西南铝业（集团）有限责任公司",
    "编制日期": "二〇二二年五月"
}}
```
#### 结果输出示例2
```json
{{
    "建设单位": "重庆彦辰新材料科技有限公司",
    "编制日期": "2024年3月"
}}
```
""".lstrip('\n')
    _q0 = q0.format(file_name=ent_name)
    base_info = ask_many_times(_q0)
    res['基本信息'] = base_info

    # 提取建设单位、编制时间、建设项目行业类别
    q0 = """
请根据环评报告"{file_name}"仔细分析回答`建设项目行业类别`是什么。
最终结果请用json格式给出，并以```json开始，以```结束。若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。
仔细分析，谨慎输出"未找到（仅供参考）"。示例如下：
#### 结果输出示例1
```json
{{
    "建设项目行业类别": "二十九有色金属冶炼和压延加工业32"
}}
```
#### 结果输出示例2
```json
{{
    "建设项目行业类别": "26-053塑料制品业292"
}}
```
""".lstrip('\n')
    _q0 = q0.format(file_name=ent_name)
    base_info = ask_many_times(_q0)
    # 将字典合并进res中
    res = {**res, **base_info}

    # 环评大气排气筒高度、内径、名称、经纬度
    q1 = """
请根据环评报告"{file_name}"仔细分析回答报告中所有大气（废气）排气筒名称、编码、高度、内径、经度、纬度，注意不要遗漏任何排气筒，
编码可能是“字母+数字”，也可能是“数字+#”，例如“DA001”、“5#”。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
最终结果请用json格式给出，并以```json开始，以```结束。没有排气筒时请输出空数组"[]"。示例如下：
#### 有废气排放口输出示例
```json
[
    {{
        "排气筒名称": "熔化废气排放口",
        "排气筒编码": "DA001",
        "排气筒高度": "18m",
        "排气筒内径": "1.2m"
        "排气筒经度": "106.32743",
        "排气筒纬度": "29.34255"
    }},{{
        "排气筒名称": "收尘系统排气筒",
        "排气筒编码": "DA002",
        "排气筒高度": "不低于10m",
        "排气筒内径": "2m"
        "排气筒经度": "106.419030°E",
        "排气筒纬度": "29.426746°N"
    }}
]
```

#### 无排气筒输出示例
```json
[]
```
""".lstrip('\n')
    _q1 = q1.format(file_name=ent_name)
    gas_outlets = ask_many_times(_q1)
    res['排气筒信息'] = gas_outlets

    # 环评水排放口名称、编码、经纬度
    q2 = """
请根据环评报告"{file_name}"仔细分析回答报告中所有废水排放口的的名称、编码、经度、纬度信息，注意不要遗漏任何废水排放口。
编码可能是“字母+数字”，也可能是“数字+#”，例如“DW001”、“5#”。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
最终结果请用json格式给出，并以```json开始，以```结束。没有废水排放口时请输出空数组"[]"。示例如下：
#### 有废水污染物排放口输出示例
```json
[
    {{
        "废水排放口名称": "依托生化池排口",
        "废水排放口编码": "DW001",
        "废水排放口经度": "106°18′18.7″",
        "废水排放口纬度": "29°19′21.8″"
    }},
    {{
        "废水排放口名称": "化粪池排口",
        "废水排放口编码": "DW002",
        "废水排放口经度": "106.419030°E",
        "废水排放口纬度": "29.426746°N"
    }}
]
```

#### 无废水污染物排放口输出示例
```json
[]
```
""".lstrip('\n')
    _q2 = q2.format(file_name=ent_name)
    water_outlets = ask_many_times(_q2)
    res['废水排放口信息'] = water_outlets

    # 环评排污单位大气排放总许可量
    q3 = """
请根据环评报告"{file_name}"仔细分析回答报告中每种废气污染物的年排放总量限值（**所有排口合计**），
描述方式可能是“本项目建成后全厂排放量”或其他说明文字，
需要你充分理解后判断，单位一般为t/a，给出每种污染物的的年排放总量限值（非零），
最终结果请用json格式给出，并以```json开始，以```结束。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
确实没有年排放限值时请输出空对象"{{}}"。示例如下：
#### 有废气污染物年排放总量限值输出示例
```json
{{
    "颗粒物": "0.236 t/a",
    "SO2": "0.01 t/a",
    "NOx": "0.22 t/a"
}}
```

#### 无废气污染物年排放总量限值输出示例
```json
{{}}
```
""".lstrip('\n')
    _q3 = q3.format(file_name=ent_name)
    ent_gas_thres = ask_many_times(_q3)
    res['大气污染物年排放总量限值'] = ent_gas_thres

    # 环评排污单位水排放许可限值
    q4 = """
请根据环评报告"{file_name}"仔细分析回答报告中每种废水污染物的年排放总量限值（**所有排口合计**），
描述方式可能是“本项目建成后全厂排放量”或其他文字，需要你充分理解后判断，单位一般为t/a，
给出每种污染物的的年排放总量限值（非零），最终结果请用json格式给出，并以```json开始，以```结束。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
确实没有年排放限值时请输出空对象"{{}}"。示例如下：
#### 有废水污染物年排放总量限值输出示例
```json
{{
    "COD": "0.32 t/a",
    "SS": "0.16 t/a"
}}
```

#### 无废水污染物年排放总量限值输出示例
```json
{{}}
```
""".lstrip('\n')
    _q4 = q4.format(file_name=ent_name)
    ent_water_thres = ask_many_times(_q4)
    res['水污染物年排放总量限值'] = ent_water_thres

    # 环评大气排口限值
    q5 = """
请根据环评报告"{file_name}"仔细分析回答下述给定排放口排放的所有废气污染物的`排放浓度限值`和`排放速率限值`，
最终结果请用json格式给出，并以```json开始，以```结束。没有时请输出空对象"{{}}"。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
示例如下：
#### 示例
##### 给定排放口示例
[
    {{
        "大气排气筒名称": "熔化废气排放口",
        "大气排气筒编码": "DA001",
        "大气排气筒高度": "18m",
        "大气排气筒内径": "1.2m"
        "大气排气筒经度": "106.32743",
        "大气排气筒纬度": "29.34255"
    }},{{
        "大气排气筒名称": "收尘系统排气筒",
        "大气排气筒编码": "DA001",
        "大气排气筒高度": "10m",
        "大气排气筒内径": "2m"
        "大气排气筒经度": "106.419030°E",
        "大气排气筒纬度": "29.426746°N"
    }}
]
##### 输出结果示例
```json
{{
    "熔化废气排放口（DA001）": {{
        "氮氧化物": {{
            "排放浓度限值": "200 mg/Nm3",
            "排放速率限值": "0.13 kg/h"
        }},
        "颗粒物": {{
            "排放浓度限值": "50 mg/Nm3",
            "排放速率限值": "0.24 kg/h"
        }}
    }},
    "收尘系统排气筒": {{
        "颗粒物": {{
            "排放浓度限值": "50 mg/Nm3",
            "排放速率限值": "0.53 kg/h"
        }},
        "二甲苯": {{
            "排放浓度限值": "21 mg/m3",
            "排放速率限值": "1.7 kg/h"
        }}
    }}
}}
```

#### 真实数据
##### 给定排放口
{port}
##### 结果输出
""".lstrip('\n')
    ent_gas_thres = {}
    if gas_outlets:
        ports = json.dumps(gas_outlets, ensure_ascii=False)
        _q5 = q5.format(file_name=ent_name, port=ports)
        ent_gas_thres = ask_many_times(_q5)
    res['大气污染物排放限值'] = ent_gas_thres

    # 环评废水排口限值
    q6 = """
请根据环评报告"{file_name}"仔细分析回答下述给定排放口排放的所有废水污染物的`排放浓度限值`和`排放速率限值`，
最终结果请用json格式给出，并以```json开始，以```结束。没有时请输出空对象"{{}}"。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
示例如下：
#### 示例
##### 给定排放口示例
[
    {{
        "废水排放口名称": "依托生化池排口",
        "废水排放口编码": "DW001",
        "废水排放口经度": "106°18′18.7″",
        "废水排放口纬度": "29°19′21.8″"
    }},
    {{
        "废水排放口名称": "化粪池排口",
        "废水排放口编码": "DW002",
        "废水排放口经度": "106.419030°E",
        "废水排放口纬度": "29.426746°N"
    }}
]
##### 输出结果示例
```json
{{
    "依托生化池排口（DW001）": {{
        "COD": {{
            "排放浓度限值": "200 mg/L",
            "排放速率限值": "1.3 kg/h"
        }},
        "NH3-N": {{
            "排放浓度限值": "50 mg/L",
            "排放速率限值": "0.83 kg/h"
        }}
    }},
    "化粪池排口（DW002）": {{
        "COD": {{
            "排放浓度限值": "200 mg/Nm3",
            "排放速率限值": "0.23 kg/h"
        }},
        "动植物油": {{
            "排放浓度限值": "100 mg/Nm3",
            "排放速率限值": "1.24 kg/h"
        }},
        "SO2": {{
            "排放浓度限值": "0.04 mg/Nm3",
            "排放速率限值": "0.16 kg/h"
        }}
    }}
}}
```

#### 真实数据
##### 给定排放口
{port}
##### 结果输出
""".lstrip('\n')
    ent_water_thres = {}
    if water_outlets:
        ports = json.dumps(water_outlets, ensure_ascii=False)
        _q6 = q6.format(file_name=ent_name, port=ports)
        ent_water_thres = ask_many_times(_q6)
    res['水污染物排放限值'] = ent_water_thres


    # 产品、主要产品、原料、辅料、原辅料
    q7 = """
请根据环评报告"{file_name}"仔细分析回答报告中的所有产品，以及每种产品的原料、辅料（原辅料）和生产工艺。
最终结果请用json格式给出，并以```json开始，以```结束。没有时请输出空数组"[]"。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
示例如下：
#### 示例
```json
[
    {{
        "产品": "农机箱体",
        "原料": "铝锭",
        "辅料": "除渣除气剂",
        "生产工艺": "熔炼、压铸、去毛刺、抛丸、机加（湿式）"
    }},
    {{
        "产品": "摩托车轮辋",
        "原料": "铝锭",
        "辅料": "油漆、稀释剂",
        "生产工艺": "熔炼、压铸、去毛刺、抛丸、机加（干式）、表面处理、组装"
    }}
]
```
""".lstrip('\n')
    _q7 = q7.format(file_name=ent_name)
    product = ask_many_times(_q7)
    res['产品'] = product

    # 储罐、油罐、储油罐、个数、公称容积
    q8 = """
请根据环评报告"{file_name}"仔细判断是否是加油站的环评报告，如果是加油站，请回答报告中的所有储油罐（储罐、油罐）的数量和容积，
如果不是加油站，请直接输出空数组“[]”。最终结果请用json格式给出，并以```json开始，以```结束。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。示例如下：
#### 加油站示例
```json
[
    {{
        "名称": "92#汽油储罐",
        "数量": "2",
        "容器": "30m3"
    }},
    {{
        "名称": "95#汽油储罐",
        "数量": "1",
        "容器": "15m3"
    }},
    {{
        "名称": "98#汽油储罐",
        "数量": "1",
        "容器": "30m3"
    }}
]
```

#### 非加油站示例
```json
[]
```
""".lstrip('\n')
    _q8 = q8.format(file_name=ent_name)
    oil = ask_many_times(_q8)
    res['储油罐'] = oil

    # 行业类别、是否为可行技术
    q9 = """
请根据环评报告"{file_name}"回答报告提到的所有污染治理设施、工艺和是否为可行技术。
最终结果请用json格式给出，并以```json开始，以```结束。
若确实没找到相应的答案，请在键值对的值中输出"未找到（仅供参考）"。仔细分析，谨慎输出"未找到（仅供参考）"。
没有提及时请输出空数组"[]"。示例如下：
#### 示例
```json
[
    {{
        "治理设施名称": "生化池",
        "治理工艺": "隔油沉砂+厌氧+生化",
        "是否为可行技术": "是"
    }},
    {{
        "治理设施名称": "总排口",
        "治理工艺": "FCR工艺",
        "是否为可行技术": "是"
    }}
]
```
""".lstrip('\n')
    _q9 = q9.format(file_name=ent_name)
    facility = ask_many_times(_q9)
    res['污染防治设施'] = facility

    return res


def copy_docx_file(source_path, destination_folder):
    """
    将指定的docx文件复制到目标文件夹

    参数:
        source_path (str): 源docx文件的完整路径
        destination_folder (str): 目标文件夹路径

    返回:
        str: 复制后的文件路径
    """
    # 检查源文件是否存在
    if not os.path.exists(source_path):
        raise FileNotFoundError(f"源文件 {source_path} 不存在")

    # 检查目标文件夹是否存在，不存在则创建
    if not os.path.exists(destination_folder):
        os.makedirs(destination_folder)

    # 获取文件名
    file_name = os.path.basename(source_path)

    # 构建目标路径
    destination_path = os.path.join(destination_folder, file_name)

    # 复制文件
    shutil.copy2(source_path, destination_path)

    return destination_path


def main(file_path):
    flatten_path = os.path.join(proj_dir, 'data', 'flatten')
    if os.path.exists(flatten_path):
        shutil.rmtree(flatten_path)
    os.makedirs(flatten_path)
    ori_full_path = copy_docx_file(file_path, flatten_path)
    file_name = os.path.basename(ori_full_path)
    # 在文件名前面添加'flatten-'
    flatten_full_path = os.path.join(flatten_path, 'flatten-' + file_name)
    doc = Document(ori_full_path)
    transition_docx_structure(doc, flatten_full_path)
    if os.path.exists(flatten_full_path):
        md_file = flatten_full_path.replace('.docx', '.md')
        convert_docx_type(flatten_full_path, md_file)
    else:
        flatten_full_path = ori_full_path

    data = extract(flatten_path, os.path.basename(flatten_full_path))
    save_path = os.path.join(proj_dir, 'data', '环评', '九龙坡区', file_name.replace('.docx', '.json'))
    save_json(save_path, data)
    return


def _upload_docs(dataset, folder):
    """ 离线从文件夹中加载文件 """
    full_path = []
    files = [file_name for file_name in os.listdir(folder)
             if file_name.endswith('.docx') and not file_name.startswith('~$')]
    if 'flatten' in ''.join(files):
        files = [file_name for file_name in files if file_name.startswith('flatten')]
    if len(files) == 1:
        full_path.append(os.path.join(folder, files[0]))
    else:
        warnings.warn(f"文件夹 {folder} 含文件{files}")

    upload_docs(dataset, full_path)
    return full_path


def get_full_path():
    full_paths = []
    base_path = '/home/<USER>/project/large_model/材料/2022年环评项目电子档案资料'
    for file in [
        '渝（九）环准〔2022〕027号九龙坡区人民医院迁建项目(一期工程)（九龙街道）/3人民医院（分期）.docx',
        '渝（九）环准〔2022〕057号重庆昆嘉机械制造有限公司摩托车轮辋及农机箱体生产项目（西彭）/摩托车轮辋及农机箱体生产项目环评表（报批稿）.docx',
        '渝（九）环准〔2022〕037号西南铝业（集团）有限责任公司 窄口拓宽项目（西彭）/1、关键工序窄口拓宽项目报批版.docx',
        '渝（九）环准〔2022〕032号重庆志成机械有限公司年产50万件汽缸头生产线前处理生产线技改项目（西彭）/06  年产50万件汽缸头生产线前处理生产线技改项目，报批版.docx'
    ]:
        full_path = os.path.join(base_path, file)
        full_paths.append(full_path)
    return full_paths


def get_whole_file():
    full_paths = []
    base_path = '/home/<USER>/project/large_model/材料/2022年环评项目电子档案资料'
    for folder in os.listdir(base_path):
        path = os.path.join(base_path, folder)
        if os.path.isdir(path):
            files = [
                file
                for file in os.listdir(path)
                if file.endswith('.docx') and not file.startswith('~$') and not file.startswith('flatten')
            ]
            if len(files) == 1:
                full_path = os.path.join(path, files[0])
                full_paths.append(full_path)
            else:
                warnings.warn(f"文件夹 {folder} 含文件{files}")
    return full_paths


if __name__ == '__main__':
    # file_paths = get_full_path()
    file_paths = get_whole_file()
    for file_path in file_paths:
        main(file_path)
