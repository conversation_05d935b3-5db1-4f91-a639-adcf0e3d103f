# -*- coding: utf-8 -*-
"""
@File  : extract_exe_data_from_pdf.py
@Author: <PERSON>
@Date  : 2025/6/18 17:49
@Desc  : 
"""
import re
import os
from tqdm import tqdm
import pandas as pd
from docx import Document
from docx.document import Document as DocumentObject
from docx.table import Table
from docx.text.paragraph import Paragraph
from wcwidth import wcswidth as _wcswidth

from lib import proj_dir


def get_document_body_elements(doc: DocumentObject):
    """
    获取文档主体部分的所有元素（段落和表格）的有序列表。

    Args:
        doc: python-docx 的 Document 对象。

    Returns:
        一个包含 Paragraph 和 Table 对象的有序列表。
    """
    body_elements = []
    # doc.element.body 包含了文档主体部分的所有顶级块元素
    for child in doc.element.body:
        # 根据元素的 XML 标签来判断是段落还是表格
        if child.tag.endswith('p'):
            body_elements.append(Paragraph(child, doc))
        elif child.tag.endswith('tbl'):
            body_elements.append(Table(child, doc))
    return body_elements


def wcswidth(pwcs, n=None, unicode_version='auto'):
    pwcs = pwcs.replace('\n', '\\n')
    length = _wcswidth(pwcs, n, unicode_version)
    cnt = pwcs.count('\\n')
    return length - cnt


def rm_line_feed(tbl):
    """ 移除dataframe中的换行符 """
    tbl = tbl.map(lambda x: x.replace('\n', '') if isinstance(x, str) else x)
    return tbl


def extract_tables_with_headers(docx_path: str) -> dict:
    """
    从指定的 .docx 文件中读取表格，合并跨页表格，并提取其前面的特定标题。

    合并规则：两个表格之间如果没有任何非空白段落，则视为同一个表格。
    标题规则：从表格向上查找，直到找到第一个不以“注”或数字开头的非空段落为止。

    Args:
        docx_path: .docx 文件的路径。

    Returns:
        一个字典，键是表格的标题，值是表格的内容（以列表的列表形式表示）。
    """
    try:
        doc = Document(docx_path)
    except Exception as e:
        print(f"错误：无法打开或读取文件 {docx_path}。异常: {e}")
        return {}

    # 获取文档中所有段落和表格的有序列表
    body_elements = get_document_body_elements(doc)

    results = {}
    i = 0
    num_elements = len(body_elements)

    while i < num_elements:
        element = body_elements[i]

        # 如果当前元素不是表格，则继续下一个
        if not isinstance(element, Table):
            i += 1
            continue

        # --- 步骤 1: 识别连续的表格组 ---
        # 发现了一个表格，这可能是一个表格组的开始
        table_group = [element]

        # 向后查找，看是否有更多表格需要合并
        next_elem_index = i + 1
        # 临时索引，用于跳过空白段落
        temp_index = next_elem_index

        while temp_index < num_elements:
            next_element = body_elements[temp_index]

            # 如果是段落，检查是否为空白
            if isinstance(next_element, Paragraph):
                if next_element.text.strip() == '':
                    # 是空白段落，继续向后看
                    temp_index += 1
                    continue
                else:
                    # 遇到非空白段落，表格组结束
                    break

            # 如果是表格，则加入表格组
            elif isinstance(next_element, Table):
                table_group.append(next_element)
                # 更新下一次循环的起始索引
                next_elem_index = temp_index + 1
                temp_index += 1
            else:
                break

        # --- 步骤 2: 合并表格数据 ---
        merged_table_data = []
        for table in table_group:
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                merged_table_data.append(row_data)

        # --- 步骤 3: 向前查找标题 ---
        header_text = ""
        # 从表格组的第一个表格前一个元素开始向上查找
        header_search_index = i - 1

        while header_search_index >= 0:
            prev_element = body_elements[header_search_index]

            if isinstance(prev_element, Paragraph):
                p_text = prev_element.text.strip()

                # 如果段落不为空，则进行判断
                if p_text:
                    # 正则表达式 `^\d` 用于判断字符串是否以数字开头
                    if not p_text.startswith('注') and not re.match(r'^\d', p_text):
                        # 找到了符合条件的标题，保存并停止查找
                        header_text = p_text
                        break

            header_search_index -= 1

        # --- 步骤 4: 存储结果 ---
        if header_text:
            # 使用找到的标题作为键，存储合并后的表格数据
            if header_text in results:
                # 处理标题重复的情况，可以附加数字后缀
                count = 2
                new_key = f"{header_text}_{count}"
                while new_key in results:
                    count += 1
                    new_key = f"{header_text}_{count}"
                results[new_key] = merged_table_data
            else:
                results[header_text] = merged_table_data

        # 更新主循环的索引，跳过已处理的整个表格组
        i = next_elem_index

    return results


def extract():
    base_path = '/home/<USER>/project/large_model/材料/2023执行报告-九龙坡'
    save_path = os.path.join(proj_dir, 'data', '执行报告', '九龙坡-2023年')
    os.makedirs(save_path, exist_ok=True)
    for ent in tqdm(os.listdir(base_path)):
        ent_path = os.path.join(base_path, ent)
        if not os.path.isdir(ent_path):
            continue
        files = [file for file in os.listdir(ent_path) if file.endswith('.docx') and not file.startswith('~$')]
        for file in files:
            if not file.startswith('convert_'):
                continue
            file_path = os.path.join(ent_path, file)
            res = extract_tables_with_headers(file_path)
            dct = {}
            for key, data in res.items():
                while len(data) > 1 and data[0] == data[1]:
                    data.pop(0)
                data = [[item.replace('\n', '') for item in row] for row in data]
                length = max([len(row) for row in data])
                data = [row + [''] * (length - len(row)) for row in data]
                data = pd.DataFrame(data[1:], columns=data[0])
                dct[key] = data

            full_path = os.path.join(save_path, file.replace('.docx', '.xlsx'))
            with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
                # 将每个DataFrame写入不同的sheet
                for key, val in dct.items():
                    key = key.replace('/', '_')
                    val.to_excel(writer, sheet_name=key, index=False)


def test():
    # --- 使用示例 ---
    try:
        # 假设当前目录下有一个名为 'your_document.docx' 的文件
        file_path = '/home/<USER>/project/large_model/材料/2023执行报告-九龙坡/气体压缩机械制造/convert_重庆建设车用空调器有限责任公司-2023年.docx'
        extracted_data = extract_tables_with_headers(file_path)

        if not extracted_data:
            print(f"在文件 '{file_path}' 中没有找到符合条件的表格和标题。")
        else:
            print(f"成功从 '{file_path}' 中提取到 {len(extracted_data)} 个表格。")
            print("-" * 50)

            for header, table_content in extracted_data.items():
                print(f"表格标题: {header}\n")
                print("表格内容:")
                for row in table_content:
                    row = [item.replace('\n', '') for item in row]
                    # 使用制表符分隔单元格，以便对齐显示
                    row = [f'{item:<10}' for item in row]
                    print("|".join(row))
                print("-" * 50)

    except FileNotFoundError:
        print(f"错误：示例文件 'your_document.docx' 未找到。")
        print("请在脚本同目录下创建一个 .docx 文件用于测试，或修改 file_path 变量。")


if __name__ == '__main__':
    extract()
