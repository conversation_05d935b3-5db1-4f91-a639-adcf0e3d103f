"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-21 4:08:13 PM

  @ FilePath:  -> srv-parse-pollutant-discharge-permit -> lib -> step_1_convert_doc_to_docx.py

  @ Description: 利用windows上的doc转docx服务转换word 2003

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-


import os
import requests
import sys

sys.path.append('../')
from lib import logger, proj_dir


def rename_recursive(file_dir: str):

    for root, _, files in os.walk(file_dir):
        for file in files:
            if not file.endswith('.docx'):
                continue
            os.rename(f'{root}/{file}', f'{root}/{file[:-1]}')

def convert_format_recursive(file_dir: str, output_dir: str):
    # 递归遍历文件目录，并根据windows的office服务将文件转换为docx格式
    base_url = 'http://************:8090/convert/'
    
    # 遍历当前目录及其子目录
    for root, _, files in os.walk(file_dir):
        if '上海' in root or '呼和浩特' in root or '北京' in root or '天津' in root:
            continue
        for file in files:
            
            if not file.endswith('.doc') or file.startswith('.') or file.startswith('~'):
                print(root, file, 'continue')
                continue
            print(root, file, 'converting')
            # 构造输入文件路径和输出文件路径
            input_file_path = os.path.join(root, file)
            relative_path = os.path.relpath(root, file_dir)
            output_subdir = os.path.join(output_dir, relative_path)
            os.makedirs(output_subdir, exist_ok=True)
            output_file_path = os.path.join(output_subdir, f"{file}x")
            
            if os.path.exists(output_file_path):
                continue
            
            with open(input_file_path, "rb") as f:
                file_tmp = [('file', (file, f, 'application/wps-office.doc'))]
                response = requests.post(base_url, files=file_tmp)
            
            # 保存转换后的文件
            if response.status_code == 200:
                with open(output_file_path, "wb") as f:
                    f.write(response.content)
                print(f"File converted and saved as '{output_file_path}'")
            else:
                print(f"Error: {response.status_code}, {response.text}")


def rename(file_dir: str):
    from lib.step_2_extract_polluant_discharge_permit_bed import DocumetParser
    files = os.listdir(file_dir)
    for file in files:
        if not file.endswith('.docx'):
            continue
        doc = DocumetParser(f'{file_dir}/{file}', 'r')
        table = doc.find_table_by_name('排污单位基本信息表')
        
        # 查询表格内的单位名称
        firm_name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
        firm_code = doc.get_table_index_by_name(table, '统一社会信用代码', col_select=True)

        # os.rename(f'{file_dir}/{file}', f'{file_dir}/{firm_code}_{firm_name}.docx')
        logger.info(f'{file} 重命名成功 → {firm_code}_{firm_name}.docx')



if __name__ == "__main__":
    # 示例调用
    # rename_recursive('/mnt/e/pol/排污许可证-无副本日期标记/')
    convert_format_recursive('/mnt/e/pol/排污许可证-无副本日期标记/', '/mnt/e/pol/排污许可证-docx/')