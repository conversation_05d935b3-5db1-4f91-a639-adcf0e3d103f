"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-21 10:02:55 PM


  @ FilePath:  -> srv-parse-pollutant-discharge-permit -> lib -> step_3_extract_polluant_discharge_port_permit.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-

import os
import re
import sys
import logging
import requests
import pandas as pd

from typing import Union, List
from docx import Document
from docx.oxml import parse_xml
from docx.table import Table
from thefuzz import process
from multiprocessing import Pool, cpu_count

sys.path.append('../')

from lib.extract_from_licence import logger, proj_dir, progress_bar
from lib.extract_from_licence.file_parser.parse_docx import PARSE_DOCX_TO_MARKDOWN
from lib.extract_from_licence.step_2_extract_polluant_discharge_permit_bed import DocumetParser, get_all_files

def parse_value_ents_port_permit(info_pd: dict, type_port: str, firm_name: str, firm_code: str):
    # 解析有组织、无组织 排污许可证不同排口、编号的排放和限值信息
    assert type_port in ['有组织', '无组织'], 'type_port must be in ["有组织", "无组织"]'
    if type_port == '有组织':
        names = ['序号', '排放口编号', '排放口名称', '污染物种类', '许可排放浓度限值', '许可排放速率限值', 
                '许可年排放量限值（t/a）-第一年', '许可年排放量限值（t/a）-第二年', '许可年排放量限值（t/a）-第三年', 
                '许可年排放量限值（t/a）-第四年', '许可年排放量限值（t/a）-第五年', '承诺更加严格排放浓度限值']
    else:
        names = ["序号", "生产设施编号/无组织排放编号", "产污环节", "污染物种类", "主要污染防治措施", 
                 "国家或地方污染物排放标准-名称", "国家或地方污染物排放标准-浓度限值", '其他信息',
                 "年许可排放量限值（t/a）-第一年", "年许可排放量限值（t/a）-第二年", 
                 "年许可排放量限值（t/a）-第三年", "年许可排放量限值（t/a）-第四年", 
                 "年许可排放量限值（t/a）-第五年", "申请特殊时段许可排放量限值"]

    value = info_pd.values[0][0]
    if value == '':
        return pd.DataFrame(columns=names+['排口类型', '单位名称', '统一社会信用代码'])
    tmp = pd.DataFrame([_.split('|') for _ in value.split('\n')], columns=names)
    tmp['排口类型'] = type_port
    tmp['单位名称'] = firm_name
    tmp['统一社会信用代码'] = firm_code

    return tmp


def process_discharge_permit_value(file, city_name):
    # logger.info(f'{city_name} -> {file} -> 开始解析')
    try:
        doc = DocumetParser(file)

        table = doc.find_table_by_name('排污单位基本信息表')
        firm_name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
        firm_code = doc.get_table_index_by_name(table, '统一社会信用代码', col_select=True)

        orgnized_info, unorgnized_info = pd.DataFrame(), pd.DataFrame()

        table = doc.find_table_by_name('大气污染物有组织排放')
        # 有组织-主要排放口  
        main_port = doc.get_table_index_by_name(table, '主要排放口', col_select=False, next_indicator='主要排放口合计')
        if isinstance(main_port, str) and main_port == 'NotFound':
            logger.warning(f'{city_name} -> {file} -> 未找到有组织: 大气污染物有组织排放-主要排放口')
            main_info = pd.DataFrame()
        else:
            main_info = parse_value_ents_port_permit(main_port, '有组织', firm_name, firm_code)

        # 有组织-一般排放口
        general_port = doc.get_table_index_by_name(table, '一般排放口', col_select=False, next_indicator='一般排放口合计')
        if isinstance(general_port, str) and general_port == 'NotFound':
            logger.warning(f'{city_name} -> {file} -> 未找到有组织: 大气污染物有组织排放-一般排放口')
            general_info = pd.DataFrame()
        else:
            general_info = parse_value_ents_port_permit(general_port, '有组织', firm_name, firm_code)
        
        orgnized_info = pd.concat([main_info, general_info], ignore_index=True, axis=0)

        # 无组织排放口
        table = doc.find_table_by_name('大气污染物无组织排放')
        unorgnized_port = doc.get_table_index_by_name(table, '序号', col_select=False, next_indicator='全厂无组织排放总计')
        if isinstance(unorgnized_info, str) and unorgnized_info == 'NotFound':
            logger.warning(f'{city_name} -> {file} -> 未找到无组织: 大气污染物无组织排放')
            unorgnized_info = pd.DataFrame()
        else:
            unorgnized_info = parse_value_ents_port_permit(unorgnized_port, '无组织', firm_name, firm_code)

        return (orgnized_info, unorgnized_info)
    except Exception as e:
        logger.error(f'{city_name} -> {file} -> 排口排放 -> 解析失败 -> {e}')
        return pd.DataFrame(), pd.DataFrame()


def get_ents_pol_discharge_permit_value(file_list: list, dir_out: str, city_name: str):
    orgnized_info, unorgnized_info = pd.DataFrame(), pd.DataFrame()
    os.makedirs(dir_out, exist_ok=True)
    bar = progress_bar(len(file_list), desc='许可排口排放-解析进度')
    def bar_update(*args, **kwargs):
        bar.update()

    results = []
    with Pool(max_processors()) as pool:
        for file in file_list:
            results.append(pool.apply_async(process_discharge_permit_value, args=(file, city_name), callback=bar_update))
        results = [res.get() for res in results]

    for org_info, unorg_info in results:
        if not org_info.empty:
            orgnized_info = pd.concat((orgnized_info, org_info), axis=0)
        if not unorg_info.empty:
            unorgnized_info = pd.concat((unorgnized_info, unorg_info), axis=0)

    orgnized_info.to_csv(f'{dir_out}/{city_name}_有组织.csv', index=False)
    unorgnized_info.to_csv(f'{dir_out}/{city_name}_无组织.csv', index=False)
    logger.info(f'{city_name} -> 排污许可证解析成功')


def max_processors(max_cpu_count=3):
    return min(max_cpu_count, cpu_count())


if __name__ == "__main__":
    logger = logging.getLogger(__name__)
    logger.handlers = [logging.FileHandler(f'{proj_dir}/data/logs/step_3_extract_polluant_discharge_port_permit.log')]
    logger.setLevel(logging.INFO)
    
    dirs_dict = dict(zip(['上海市', '北京市', '太原市', '呼和浩特市', '天津市'], 
        ['/mnt/e/pol/排污许可证-docx/上海市', 
        '/mnt/e/pol/排污许可证-docx/北京市',  
        '/mnt/e/pol/排污许可证-docx/山西省',
        '/mnt/e/pol/排污许可证-docx/内蒙古自治区',
        '/mnt/e/pol/排污许可证-docx/天津市']
    ))
    for city_name, dirs_in in dirs_dict.items():
        # dirs_in = '/mnt/e/pol/排污许可证-docx/上海市'
        # city_name = '上海市'
        files_in = get_all_files(dirs_in)
        dirs_out=f'{proj_dir}/data/parsed/{city_name}/'

        # 获取有组织/无组织/总排放排口信息
        get_ents_pol_discharge_permit_value(files_in, dirs_out, city_name=city_name)
