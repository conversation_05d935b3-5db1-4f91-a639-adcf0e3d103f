"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-21 4:08:03 PM

  @ FilePath:  -> srv-parse-pollutant-discharge-permit -> lib -> step_0_format_converted_on_windows.py

  @ Description: 必须在 windows 系统下运行, 需要pywin32、fastapi、uvicorn的支持,删除转换成功的文件

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-



import os
import win32com.client as win32
from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import pythoncom
import logging
import traceback
from multiprocessing import Pool

app = FastAPI()

# 配置日志记录
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 文件格式映射
FILE_FORMATS = {
    # Word 格式
    "doc": 0,    # wdFormatDocument (Word 97-2003)
    "docx": 16,  # wdFormatDocumentDefault (Office 2007+ 默认格式)
    "docm": 13,  # wdFormatXMLDocumentMacroEnabled (启用宏的文档)
    
    # XML 格式
    "xml": 11,   # wdFormatXMLDocument (Word 2003 XML)
    "xmlx": 12,  # wdFormatXMLDocumentMacroFree
    
    # PDF/XPS
    "pdf": 17,   # wdFormatPDF
    "xps": 18,   # wdFormatXPS
    
    # 网页格式
    "html": 8,   # wdFormatHTML
    "htm": 8,    # 同上
    "mhtml": 15, # wdFormatWebArchive
    
    # 纯文本
    "txt": 2,    # wdFormatText (无格式)
    "rtf": 6,    # wdFormatRTF
    
    # 其他办公格式
    "odt": 23,   # wdFormatOpenDocumentText

}

proj_dir = os.path.dirname(os.path.abspath(__file__))

def convert_file(input_path: str, output_format: str) -> str:
    logger.info(f"开始转换文件: {input_path} -> 格式: {output_format}")
    word = None  # 初始化为 None
    doc = None  # 初始化为 None
    try:
        pythoncom.CoInitialize()  # 初始化 COM 库
        word = win32.Dispatch("Word.Application")
        if not word:  # 检查 Word 应用程序是否正确初始化
            raise RuntimeError("无法初始化 Microsoft Word 应用程序")
        word.DisplayAlerts = 0  # 禁用弹窗
        word.AutomationSecurity = 3  # 禁用宏警告（msoAutomationSecurityForceDisable）

        if not word:  # 检查 Word 应用程序是否正确初始化
            raise RuntimeError("无法初始化 Microsoft Word 应用程序")
        # 验证文件是否存在
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"文件不存在: {input_path}")

        # 打开文件（只读模式，启用内容）
        try:
            if not hasattr(word, "Documents"):
                raise RuntimeError("Word 对象缺少 Documents 属性")
            doc = word.Documents.Open(
                input_path,
                ReadOnly=True,  # 以只读模式打开
                AddToRecentFiles=False,  # 不添加到最近文件列表
                OpenAndRepair=True  # 尝试修复并启用内容
            )
        except Exception as e:
            print(traceback.format_exc())
            raise RuntimeError(f"无法打开文件: {input_path}, 错误: {str(e)}")

        # 验证目标格式
        if output_format not in FILE_FORMATS:
            raise ValueError(f"不支持的目标格式: {output_format}")

        # 保存文件
        base_name = os.path.splitext(input_path)[0]
        output_path = f"{base_name}.{output_format}"
        try:
            doc.SaveAs2(
                FileName=output_path,
                FileFormat=FILE_FORMATS[output_format]
            )
            logger.info(f"文件保存成功: {output_path}")
        except Exception as e:
            print(traceback.format_exc())
            logger.warning(f"保存文件失败: {output_path}, 错误: {str(e)}")
            output_path = None  # 标记保存失败
        return output_path
    except Exception as e:
        print(traceback.format_exc())
        logger.error(f"文件转换失败: {input_path}, 错误: {str(e)}")
    finally:
        if doc:  # 确保文档对象存在
            try:
                doc.Close()
            except Exception as close_error:
                logger.warning(f"关闭文档时出错: {str(close_error)}")
        if word:  # 确保 Word 应用程序对象存在
            try:
                word.Quit()
            except Exception as quit_error:
                logger.warning(f"关闭 Word 应用程序时出错: {str(quit_error)}")
        pythoncom.CoUninitialize()


def cleanup_files(*paths):
    """清理临时文件"""
    for path in paths:
        if os.path.exists(path):
            try:
                os.remove(path)
                logger.info(f"清理临时文件: {path}")
            except Exception as e:
                logger.warning(f"清理文件失败: {path}, 错误: {str(e)}")


@app.post("/convert/")
async def convert_endpoint(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    target_format: str = "docx"
):
    logger.info(f"收到单文件转换请求: {file.filename} -> 格式: {target_format}")
    # 验证目标格式
    if target_format not in FILE_FORMATS:
        raise HTTPException(400, "不支持的格式，仅支持 docx 或 xml")
    
    # 验证文件类型
    file_ext = file.filename.split('.')[-1].lower()
    if file_ext not in ["doc", "docx"]:
        raise HTTPException(400, "仅支持 DOC 或 DOCX 文件")

    # 保存上传文件
    input_path = os.path.join(proj_dir, file.filename)
    with open(input_path, "wb") as f:
        f.write(await file.read())

    try:
        output_path = convert_file(input_path, target_format)
    except Exception as e:
        raise HTTPException(500, detail=str(e))

    # 添加清理任务
    background_tasks.add_task(cleanup_files, input_path, output_path)

    logger.info(f"单文件转换完成: {output_path}")
    # 返回转换后的文件
    return FileResponse(
        output_path,
        filename=os.path.basename(output_path),
        media_type="application/octet-stream"
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)