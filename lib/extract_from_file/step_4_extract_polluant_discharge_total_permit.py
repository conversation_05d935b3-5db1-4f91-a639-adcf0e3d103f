"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-21 9:56:57 PM

  @ FilePath:  -> srv-parse-pollutant-discharge-permit -> lib -> step_4_extract_polluant_discharge_total_permit.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-


import os
import re
import sys
import logging
import requests
import pandas as pd

from typing import Union, List
from docx import Document
from docx.oxml import parse_xml
from docx.table import Table
from thefuzz import process
from multiprocessing import Pool, cpu_count

sys.path.append('../')

from lib.extract_from_licence import logger, proj_dir, progress_bar
from lib.extract_from_licence.step_2_extract_polluant_discharge_permit_bed import get_all_files, DocumetParser
from lib.extract_from_licence.file_parser.parse_docx import PARSE_DOCX_TO_MARKDOWN


def parse_value_ents_total_permit(value: str, type_pol: str, name_pol:str, firm_name: str, firm_code: str):
    
    assert type_pol in ['有组织', '无组织', '总排放'], 'type_pol must be in ["有组织", "无组织", "总排放"]'
    names = ['污染物名称','许可年排放量限值（t/a）-第一年', '许可年排放量限值（t/a）-第二年', '许可年排放量限值（t/a）-第三年', 
            '许可年排放量限值（t/a）-第四年', '许可年排放量限值（t/a）-第五年']
    if type_pol == '有组织':
        names += ['承诺更加严格排放浓度限值']
    elif type_pol == '无组织':
        names += ['申请特殊时段许可排放量限值']
    elif type_pol == '总排放':
        names = ['序号'] + names
    
    if value == '':
        return pd.DataFrame(columns=names+['排口类型', '单位名称', '统一社会信用代码'])
    tmp = pd.DataFrame([_.split('|') for _ in value.split('\n')], columns=names)
    tmp['排口类型'] = name_pol
    tmp['单位名称'] = firm_name
    tmp['统一社会信用代码'] = firm_code

    return tmp
    

def process_discharge_permit(file, city_name):
    try:
        doc = DocumetParser(file)
        table = doc.find_table_by_name('排污单位基本信息表')
        firm_name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
        firm_code = doc.get_table_index_by_name(table, '统一社会信用代码', col_select=True)

        orgnized_info, unorgnized_info, total_permit = pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        table = doc.find_table_by_name('大气污染物有组织排放')
        for indicator in ['主要排放口合计', '一般排放口合计', '全厂有组织排放总计']:
            _port = doc.get_table_index_by_name(table, indicator, col_select=True)
            if _port == 'NotFound':
                logger.info(f'{city_name} -> {file} -> {indicator} -> 解析失败 -> 没有查询到{indicator}')
                continue
            orgnized_info = pd.concat((orgnized_info, parse_value_ents_total_permit(_port, '有组织', indicator, firm_name, firm_code)), axis=0)

        table = doc.find_table_by_name('大气污染物无组织排放')
        indicator = '全厂无组织排放总计'
        _port = doc.get_table_index_by_name(table, indicator, col_select=True)
        if isinstance(_port, str) and _port == 'NotFound':
            logger.info(f'{city_name} -> {file} -> {indicator} -> 解析失败 -> 没有查询到{indicator}')
        else:
            unorgnized_info = pd.concat((unorgnized_info, parse_value_ents_total_permit(_port, '无组织', indicator, firm_name, firm_code)), axis=0)

        table = doc.find_table_by_name('企业大气排放总许可量')
        indicator = '序号'
        _port = doc.get_table_index_by_name(table, indicator, col_select=False)
        if isinstance(_port, str) and  _port == 'NotFound':
            logger.info(f'{city_name} -> {file} -> {indicator} -> 解析失败 -> 没有查询到{indicator}')
        else:
            total_permit = pd.concat((total_permit, parse_value_ents_total_permit(_port, '总排放', '企业大气排放总许可量', firm_name, firm_code)), axis=0)

        return orgnized_info, unorgnized_info, total_permit
    except Exception as e:
        logger.error(f'{city_name} -> {file} -> 总排放 -> 解析失败 -> {e}')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

def get_ents_pol_discharge_permit_value_total(file_list: list, dir_out: str, city_name: str):
    os.makedirs(dir_out, exist_ok=True)
    orgnized_info, unorgnized_info, total_permit = pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    bar = progress_bar(len(file_list), desc='许可总排放-解析进度')
    def bar_update(*args, **kwargs):
        bar.update()

    results = []
    with Pool(max_processors()) as pool:
        for file in file_list:
            results.append(pool.apply_async(process_discharge_permit, args=(file, city_name,), callback=bar_update))
        results = [res.get() for res in results]

    for org_info, unorg_info, total_info in results:
        if not org_info.empty:
            orgnized_info = pd.concat((orgnized_info, org_info), axis=0)
        if not unorg_info.empty:
            unorgnized_info = pd.concat((unorgnized_info, unorg_info), axis=0)
        if not total_info.empty:
            total_permit = pd.concat((total_permit, total_info), axis=0)

    orgnized_info.to_csv(f'{dir_out}/{city_name}_有组织总排放.csv', index=False)
    unorgnized_info.to_csv(f'{dir_out}/{city_name}_无组织总排放.csv', index=False)
    total_permit.to_csv(f'{dir_out}/{city_name}_总排放.csv', index=False)
    logger.info(f'{city_name} -> 总排放解析成功')


def max_processors(max_cpu_count=3):
    return min(max_cpu_count, cpu_count())


if __name__ == "__main__":
    # dirs = '/mnt/c/Users/<USER>/Desktop/九龙坡项目/排污许可证/九龙坡230家排污许可证-doc/'
    logger = logging.getLogger(__name__)
    logger.handlers = [logging.FileHandler(f'{proj_dir}/data/logs/step_4_extract_polluant_discharge_total_permit.log')]
    logger.setLevel(logging.INFO)

    dirs_dict = dict(zip(['上海市', '北京市', '太原市', '呼和浩特市', '天津市'], 
        ['/mnt/e/pol/排污许可证-docx/上海市', 
        '/mnt/e/pol/排污许可证-docx/北京市',  
        '/mnt/e/pol/排污许可证-docx/山西省',
        '/mnt/e/pol/排污许可证-docx/内蒙古自治区',
        '/mnt/e/pol/排污许可证-docx/天津市']
    ))
    for city_name, dirs_in in dirs_dict.items():
        # dirs_in = '/mnt/e/pol/排污许可证-docx/上海市'
        # city_name = '上海市'
        files_in = get_all_files(dirs_in)
        dirs_out=f'{proj_dir}/data/parsed/{city_name}/'
    
        # 获取有组织/无组织/总排放
        get_ents_pol_discharge_permit_value_total(files_in, dirs_out, city_name=city_name)
