# -*- coding: utf-8 -*-
"""
Created on 2025/2/7 上午10:47

@Project -> File: srv-pollution-jiulongpo-emission -> request_token.py

@Author: <PERSON><PERSON><PERSON>

@Describe:
"""
import sys
import json
import requests
import warnings
sys.path.append('../..')
# from mod.tool.dir_file_op import save_json_file
warnings.filterwarnings('ignore')


def get_llm_token():
    """获取华为云部署大模型访问所需token"""
    url = 'https://iam-pub.cn-southwest-6034.chongqingxc02.huaweigovcloud.com/v3/auth/tokens'

    payload = {"auth": {"identity": {"methods": ["password"], "password": {
        "user": {"name": "jlpqsthjj_2_ModelArts", "password": "S2nW2MKD@Huawei123", "domain": {"name": "jlpqsthjj_2"}}}},
                        "scope": {"project": {"name": "cn-southwest-6034"}}}}
    headers = {'Content-Type': 'application/json'}

    rst = requests.post(url = url, headers=headers, data = json.dumps(payload), verify = False)
    assert rst.status_code < 400, '获取华为云部署大模型访问所需token出错'
    token = rst.headers['X-Subject-Token']
    # save_json_file(token, './token.json')
    return token


if __name__ == '__main__':
    get_llm_token()


