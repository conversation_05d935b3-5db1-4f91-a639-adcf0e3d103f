# -*- coding: utf-8 -*-
"""
@File  : check_no_change_val.py.py
@Author: <PERSON>
@Date  : 2025/1/7 14:38
@Desc  : 执行报告记载的污染物排放量数据恒值
"""
import re
import os
import numpy as np
import pandas as pd
from collections import defaultdict
from tqdm import tqdm
import json

from lib import logger
from lib.check.base import process_doc_all_tbls
from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx
from lib.check.base import clean_null, get_exe_data, list_diff
from lib.check.base import *


def calc_last_period(txt):
    """
    计算当前月/季/年报的上一个时间

    参数:
    txt (str): 包含时间信息的字符串

    返回:
    str: 替换为上一个时间的字符串
    """
    # 匹配年份模式
    y_pattern = r'-(\d{4})年\.docx$'
    match = re.search(y_pattern, txt)
    if match:
        # 获取匹配的年份字符串
        group = match.group(0)
        # 提取年份并转换为整数
        year = int(match.group(1))
        # 计算上一年
        year -= 1
        # 构造新的年份字符串
        last_group = f"-{year}年.docx"
        # 替换原始字符串中的年份
        last_txt = txt.replace(group, last_group)
        return last_txt

    # 匹配季度模式
    q_pattern = r'-(\d{4})年第(\d{2})季\.docx$'
    match = re.search(q_pattern, txt)
    if match:
        # 获取匹配的季度字符串
        group = match.group(0)
        # 提取年份并转换为整数
        year = int(match.group(1))
        # 提取季度并转换为整数
        quarter = int(match.group(2))
        # 计算上一个季度
        if quarter == 1:
            quarter = 4
            year -= 1
        else:
            quarter = int(quarter) - 1
        # 构造新的季度字符串
        last_group = f"-{year}年第{quarter:02d}季.docx"
        # 替换原始字符串中的季度
        last_txt = txt.replace(group, last_group)
        return last_txt

    # 匹配月份模式
    m_quarter = r'-(\d{4})年(\d{2})月\.docx$'
    match = re.search(m_quarter, txt)
    if match:
        # 获取匹配的月份字符串
        group = match.group(0)
        # 提取年份并转换为整数
        year = int(match.group(1))
        # 提取月份并转换为整数
        month = int(match.group(2))
        # 计算上一个月
        if month == 1:
            month = 12
            year -= 1
        else:
            month = int(month) - 1
        # 构造新的月份字符串
        last_group = f"-{year}年{month:02d}月.docx"
        # 替换原始字符串中的月份
        last_txt = txt.replace(group, last_group)
        return last_txt


def filter_same_val_by_col(tbl1, tbl2, col):
    """
    判断两个表的指定列的值是否相同，行列索引可以是多层索引
    col: str or tuple
    """
    # 定义一个转换函数，用于将特定值转换为空字符串或浮点数
    def convert_val(val):
        if val == '' or val == '/':
            return ''
        val = float(val)
        return val

    # 初始化两个集合，用于存储每个表中指定列的非空值的行索引和值
    tbl1_set = set()
    tbl2_set = set()

    # 遍历第一个表的每一行，将指定列的非空值及其行索引添加到集合中
    for idx, row in tbl1.iterrows():
        val = row[col]
        try:
            val = convert_val(val)
            if val:
                tbl1_set.add(idx+(val,))
        except:
            raise RuntimeError(idx+(str(val),))

    # 遍历第二个表的每一行，将指定列的非空值及其行索引添加到集合中
    for idx, row in tbl2.iterrows():
        val = row[col]
        try:
            val = convert_val(val)
            if val:
                tbl2_set.add(idx+(val,))
        except:
            raise RuntimeError(idx+(str(val),))

    # 计算两个集合的交集，即两个表中指定列具有相同非空值的行
    intersect = tbl1_set.intersection(tbl2_set)
    return intersect


def filter_same_val(old_tables, new_tables, _type):
    """
    对当前docx与上一期docx进行恒值校验

    本函数旨在比较新旧两期的排放数据表，针对废气和废水的实际排放量进行对比，
    以识别在不同时间周期（月、季度、年）内排放口的排放量是否保持恒定。

    参数:
    - old_tables: 字典，包含上一期的废气和废水排放数据
    - new_tables: 字典，包含当前期的废气和废水排放数据
    - _type: 字符串，指定时间周期类型，可为'month'、'quarter'或'year'

    返回:
    - same_vals: 字典，包含在指定周期内废气和/或废水排放量保持恒定的排放口索引
    """
    # 定义一个内部函数，用于处理排放口编码及名称索引
    def process_code_name(data):
        """
        将多层索引中的 `排放口编码及名称` 索引全部统一成排放口编码
        这是为了后续比较过程中的一致性和准确性
        """
        idxs_name = data.index.names
        if '排放口编码及名称' not in idxs_name:
            return data
        code_idx = idxs_name.index('排放口编码及名称')
        codes_name = data.index.get_level_values('排放口编码及名称')
        codes = [x.split('-')[0] if '-' in x else x for x in codes_name]
        all_codes = np.array(data.index.to_list())
        all_codes[:, code_idx] = codes
        data.index = pd.MultiIndex.from_arrays(all_codes.T, names=idxs_name)
        return data

    # 初始化一个字典，用于存储恒值校验的结果
    same_vals = {}

    # 对废气和废水数据分别进行编码及名称索引的统一处理
    old_gas = process_code_name(old_tables['废气'])
    new_gas = process_code_name(new_tables['废气'])
    old_water = process_code_name(old_tables['废水'])
    new_water = process_code_name(new_tables['废水'])

    # 根据指定的时间周期类型，选择相应的排放量列进行比较
    match _type:
        case 'month':
            col = '实际排放量（吨）'
        case 'quarter':
            col = ('实际排放量（吨）','季度合计')
        case 'year':
            col = ('实际排放量（吨）','年度合计')
        case _:
            raise ValueError("_type must be 'month', 'quarter' or 'year'")

    # 调用外部函数filter_same_val_by_col对废气和废水数据进行恒值校验
    gas_same_idxs = filter_same_val_by_col(old_gas, new_gas, col)
    water_same_idxs = filter_same_val_by_col(old_water, new_water, col)

    # 将恒值校验的结果添加到返回字典中
    if gas_same_idxs:
        same_vals['废气'] = gas_same_idxs
    if water_same_idxs:
        same_vals['废水'] = water_same_idxs

    # 返回恒值校验的结果字典
    return same_vals


def type_analyze(txt):
    """ 文件类型分析：年/季/月报 """
    if txt.endswith('年.docx'):
        return 'year'
    elif txt.endswith('季.docx'):
        return 'quarter'
    elif txt.endswith('月.docx'):
        return 'month'
    else:
        raise ValueError(f"无法判断文件`{txt}`类型")


def check_company_same_val(path):
    """
    校验一个企业所有执行报告中的恒值

    该函数通过比较不同时间段的报告文件中的表格数据，找出在不同时间点之间没有发生变化的数值，
    以帮助用户识别数据报告中可能存在的恒定值问题或数据更新问题。

    参数:
    - path (str): 包含报告文件的目录路径

    返回:
    - dict: 一个字典，键为报告文件名对（last_file_name, file_name），值为这两个文件中相同的数值
    """
    # 过滤掉临时文件，获取目录下所有需要处理的文件
    files = [file for file in os.listdir(path) if not file.startswith('~$')]

    # 提取所有文件中的全部表格
    all_tables = defaultdict(dict)
    for file_name in files:
        full_path = os.path.join(path, file_name)
        docx = MarkdownConverterWithPythonDocx(full_path)
        tables = docx.tables(format_='dataframe')
        _type = type_analyze(full_path)
        tables = process_doc_all_tbls(tables, _type)
        all_tables[_type][file_name] = tables

    # 初始化结果字典
    res = {}
    for _type in all_tables:
        for file_name in all_tables[_type]:
            # 计算当前文件名对应上一个时间段的文件名
            last_file_name = calc_last_period(file_name)
            # 如果上一个时间段的文件不存在，则跳过当前文件
            if last_file_name not in all_tables[_type]:
                continue
            # 比较当前文件和上一个时间段的文件中的数值，找出相同的数值
            same_val = filter_same_val(all_tables[_type][last_file_name], all_tables[_type][file_name], _type)
            # 如果存在相同的数值，则将其添加到结果中
            if same_val:
                res[(last_file_name, file_name)] = same_val
    # 返回结果字典
    return res


def check_companies_same_val(path):
    """ 校验所有企业所有执行报告中的恒值 """
    companies = [item for item in os.listdir(path) if not item.endswith('~$')]
    res = {}
    for company in tqdm(companies):
        company_path = os.path.join(path, company)
        same_val = check_company_same_val(company_path)
        if same_val:
            res[company] = same_val
    return res


def _test_one_month():
    base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年重点企业执行报告'
    old_path = os.path.join(base_path, '重庆和友实业股份有限公司/执行报告-重庆和友实业股份有限公司-2022年04月.docx')
    new_path = os.path.join(base_path, '重庆和友实业股份有限公司/执行报告-重庆和友实业股份有限公司-2022年05月.docx')
    docx = MarkdownConverterWithPythonDocx(old_path)
    old_tables = docx.tables(format_='dataframe')
    old_tables = process_doc_all_tbls(old_tables, 'month')
    docx = MarkdownConverterWithPythonDocx(new_path)
    new_tables = docx.tables(format_='dataframe')
    new_tables = process_doc_all_tbls(new_tables, 'month')
    filter_same_val(old_tables, new_tables, 'month')


def _test_calc_last_period():
    txt = "执行报告-重庆和友实业股份有限公司-2022年01月.docx"
    print(txt)
    last_txt = calc_last_period(txt)
    print(last_txt)


def _test_check_company_same_val():
    base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年重点企业执行报告'
    path = os.path.join(base_path, '重庆和友实业股份有限公司')
    res = check_company_same_val(path)
    print(res)


def print_format(data, prefix=4, level=0, sort=True):
    """
    格式化打印恒值信息

    该函数是一个生成器函数，用于以树状结构格式化打印字典、集合等数据结构。

    参数:
    - data: 要格式化打印的数据，可以是字典、集合、字符串、整数或浮点数。
    - prefix: 每一级别的缩进空格数，默认为4。
    - level: 当前递归的级别，默认为0，用于计算缩进。
    - sort: 是否对字典的键或集合中的元素进行排序，默认为True。

    返回:
    生成器，逐行生成格式化后的字符串。
    """
    # 根据当前级别计算缩进空格
    _prefix = ' ' * prefix * (level+1)
    last_prefix = ' ' * prefix * level

    # 如果data是字典类型
    if isinstance(data, dict):
        yield '{'
        first = True
        # 根据sort参数决定是否对字典进行排序
        if sort:
            data = sorted(data.items())
        else:
            data = data.items()
        for key, val in data:
            # 格式化字典的键值对
            if first:
                first = False
                yield f'\n{_prefix}{key}: '
            else:
                yield f',\n{_prefix}{key}: '
            yield from print_format(val, prefix, level+1)
        yield f'\n{last_prefix}}}'
    # 如果data是集合类型
    elif isinstance(data, set):
        if sort:
            data = sorted(data)
        yield '{'
        first = True
        for val in data:
            # 格式化集合中的元素
            if first:
                first = False
                yield f'\n{_prefix}{val}'
            else:
                yield f',\n{_prefix}{val}'
        yield f'\n{last_prefix}}}'
    # 如果data是基本数据类型（字符串、整数、浮点数）
    elif isinstance(data, (str, int, float)):
        yield f'{last_prefix}{data}'
    # 如果data是不支持的数据类型
    else:
        raise ValueError(f"{type(data)} is not supported")


def dbbase_calc_last_period(txt):
    """
    2022年  2022年第3季  2022年4月
    """
    year = int(txt[:4])
    if txt.endswith('月'):
        month = int(txt.split('年')[-1][:-1])
        if month > 1:
            month -= 1
        else:
            month = 12
            year -= 1
        return f'{year}年{month}月'
    elif txt.endswith('季'):
        quarter = int(txt[-2:-1])
        if quarter > 1:
            quarter -= 1
        else:
            quarter = 4
            year -= 1
        return f'{year}年第{quarter}季'
    else:
        year -= 1
        return f'{year}年'


def check_tbl(start, end, ent_data, whole=True):
    """ 执行报告记载的污染物排放量数据恒值
        whole:
            True （全部数据一致且非零）
            False（单项污染物数据连续3个周期数据一致，仅对排放量大于0.1吨（月）、0.1吨（季）、0.1吨（年）的污染物进行校验）
    """
    start_year, end_year = int(start[:4]), int(end[:4])
    start_month, end_month = int(start.split('-')[1]), int(end.split('-')[1])
    start_quarter, end_quarter = (end_month + 2) // 3, (end_month + 2) // 3
    exception = {
        'gas': [],
        'water': []
    }

    def same_tbls(tbl1, tbl2, kcols, vcols):
        """ 判断两个表格的数值是否完全相同
            不全为0
        """
        tbl1 = tbl1[kcols+vcols].copy()
        tbl2 = tbl2[kcols+vcols].copy()
        tbl1[vcols] = tbl1[vcols].apply(pd.to_numeric, errors='coerce')
        tbl2[vcols] = tbl2[vcols].apply(pd.to_numeric, errors='coerce')
        tbl = pd.merge(tbl1, tbl2, on=kcols, how='inner', suffixes=('_1', '_2'))
        tbl = tbl.fillna(0)
        tbl1_vcols = [ f'{col}_1' for col in vcols]
        tbl2_vcols = [ f'{col}_2' for col in vcols]
        if (
            not tbl.empty and
            (tbl[tbl1_vcols].values != 0).all() and
            (tbl[tbl1_vcols].values == tbl[tbl2_vcols].values).all()
        ):
            return True
        return False


    def pipeline1(curr, last, ent_data, category, emission_map, base_cols):
        """ 整表恒值校验流程
            category: 'year', 'quarter', 'month'
        """
        curr_data = ent_data[curr]
        last_data = ent_data[last]

        # 废气排放表校验
        valid = False
        for emission in ['airMainEmission', 'airMinorEmission', 'airTotalEmission']:
            if emission not in curr_data or emission not in last_data:
                continue
            curr_emission = curr_data[emission]
            last_emission = last_data[emission]
            for item in [curr_emission, last_emission]:
                if 'pollutantCode' not in item:
                    item['pollutantCode'] = ''
                if emission == 'airMainEmission':
                    for col in ['outletName', 'outletCode']:
                        if col not in item:
                            item[col] = ''
            kcols = list_diff(emission_map[emission], base_cols)

            if curr_emission.empty or last_emission.empty:
                continue

            if same_tbls(curr_emission, last_emission, kcols, base_cols):
                valid = True
            else:
                break
        else:
            if valid:
                exception['gas'].append({
                    'reoport_type': category,
                    'last_report_time': last,
                    'current_report_time': curr
                })

        # 废水排放表校验
        valid = False
        for emission in ['waterMainEmission', 'waterMinorEmission', 'waterTotalEmission']:
            if emission not in curr_data or emission not in last_data:
                continue
            curr_emission = curr_data[emission]
            last_emission = last_data[emission]
            kcols = list_diff(emission_map[emission], base_cols)

            if curr_emission.empty or last_emission.empty:
                continue

            for item in [curr_emission, last_emission]:
                if 'pollutantCode' not in item:
                    item['pollutantCode'] = ''
                if emission == 'waterMainEmission':
                    for col in ['outletName', 'outletCode']:
                        if col not in item:
                            item[col] = ''

            if same_tbls(curr_emission, last_emission, kcols, base_cols):
                valid = True
            else:
                break
        else:
            if valid:
                exception['water'].append({
                    'reoport_type': category,
                    'last_report_time': last,
                    'current_report_time': curr
                })


    def pipeline2(curr, last, last_last, ent_data, category, emission_map, base_cols):
        """ 单项污染物数据连续周期数据一致，仅对排放量大于0.01吨的污染物进行校验
            category: 'year', 'quarter', 'month'
        """
        curr_data = ent_data[curr]
        last_data = ent_data[last]
        last_last_data = ent_data[last_last]

        thres = {
            'month': 0.01,
            'quarter': 0.01,
            'year': 0.01
        }[category]

        for emission in [
            'airMainEmission', 'airMinorEmission', 'airTotalEmission',
            'waterMainEmission', 'waterMinorEmission', 'waterTotalEmission'
        ]:
            if emission not in curr_data or emission not in last_data or emission not in last_last_data:
                continue

            curr_emission = curr_data[emission]
            last_emission = last_data[emission]
            last_last_emission = last_last_data[emission]
            kcols = list_diff(emission_map[emission], base_cols)

            if curr_emission.empty or last_emission.empty or last_last_emission.empty:
                continue

            for item in [curr_emission, last_emission, last_last_emission]:
                if 'pollutantCode' not in item:
                    item['pollutantCode'] = ''
                if emission in ['waterMainEmission', 'airMainEmission']:
                    if 'outletName' not in item:
                        item['outletName'] = ''

            curr_emission = curr_emission[kcols+base_cols].copy()
            last_emission = last_emission[kcols+base_cols].copy()
            last_last_emission = last_last_emission[kcols+base_cols].copy()
            curr_emission[base_cols] = curr_emission[base_cols].apply(pd.to_numeric, errors='coerce')
            last_emission[base_cols] = last_emission[base_cols].apply(pd.to_numeric, errors='coerce')
            last_last_emission[base_cols] = last_last_emission[base_cols].apply(pd.to_numeric, errors='coerce')
            tbl = pd.merge(last_emission, curr_emission, on=kcols, how='inner', suffixes=('_1', '_2'))
            tbl = tbl.merge(last_last_emission, on=kcols, how='inner', suffixes=('', '_3'))
            tbl = tbl.fillna(0)
            tbl1_vcols = [f'{col}_1' for col in base_cols]
            tbl2_vcols = [f'{col}_2' for col in base_cols]
            tbl3_vcols = [f'{col}' for col in base_cols]
            tbl.set_index(kcols, inplace=True)
            tbl.rename_axis('time', axis=1, inplace=True)
            tbl = tbl[tbl1_vcols].stack()[
                (tbl[tbl1_vcols].stack().values == tbl[tbl2_vcols].stack().values) &
                (tbl[tbl1_vcols].stack().values == tbl[tbl3_vcols].stack().values) &
                (tbl[tbl1_vcols].stack().values >= thres)
            ].reset_index(name='value')
            if not tbl.empty:
                tbl['time'] = tbl['time'].str.replace('_1', '')
                val = 'value'
                idx = [col for col in tbl.columns if col != val]
                g_s = 'gas' if emission.startswith('air') else 'water'
                for location, val in zip(tbl[idx].values.tolist(), tbl[val].to_list()):
                    exception[g_s].append({
                        'last_last_report_time': last_last,
                        'last_report_time': last,
                        'current_report_time': curr,
                        'location': location,
                        'value': val
                    })
        return exception

    # pipeline = pipeline1 if whole else pipeline2

    # 月报恒值校验
    for year in range(start_year, end_year + 1):
        for month in range(1, 13):
            if year == start_year and month < start_month:
                continue
            if year == end_year and month > end_month:
                continue

            # 月报不存在
            full_month = f'{year}年{month}月'
            last_month = dbbase_calc_last_period(full_month)
            last_last_month = dbbase_calc_last_period(last_month)
            if full_month not in ent_data or last_month not in ent_data or last_last_month not in ent_data:
                continue

            if whole:
                pipeline1(full_month, last_month, ent_data, 'month', m_emission_map, _m_base_cols)
            else:
                pipeline2(full_month, last_month, last_last_month, ent_data, 'month',
                          m_emission_map, _m_base_cols)

    # 季报恒值校验
    for year in range(start_year, end_year + 1):
        for quarter in range(1, 5):
            if year == start_year and quarter < start_quarter:
                continue
            if year == end_year and quarter > end_quarter:
                continue

            # 季报不存在
            full_quarter = f'{year}年第{quarter}季'
            last_quarter = dbbase_calc_last_period(full_quarter)
            last_last_quarter = dbbase_calc_last_period(last_quarter)
            if full_quarter not in ent_data or last_quarter not in ent_data or last_last_quarter not in ent_data:
                continue

            if whole:
                pipeline1(full_quarter, last_quarter, ent_data, 'quarter', q_emission_map, _q_base_cols)
            else:
                pipeline2(full_quarter, last_quarter, last_last_quarter, ent_data, 'quarter',
                          q_emission_map, _q_base_cols)

    # 年报恒值校验
    for year in range(start_year, end_year + 1):
        full_year = f'{year}年'
        last_year = dbbase_calc_last_period(full_year)
        last_last_year = dbbase_calc_last_period(last_year)
        if full_year not in ent_data or last_year not in ent_data or last_last_year not in ent_data:
            continue

        if whole:
            pipeline1(full_year, last_year, ent_data, 'year', y_emission_map, _y_base_cols)
        else:
            pipeline2(full_year, last_year, last_last_year, ent_data, 'year', y_emission_map, _y_base_cols)

    return exception


def dbbase_run(city, start, end, table=True):
    data = get_exe_data(city)
    exception = defaultdict(dict)
    for (ent_id, ent_name), group in data.groupby(['f_enterprise_id', 'f_enterprise_name']):
        dct = defaultdict(dict)
        for _, row in group.iterrows():
            time = row['f_report_time']
            content = row['f_report_content']
            try:
                content = json.loads(content)
            except:
                pass
            content = content['data']
            try:
                if isinstance(content, str):
                    content = content.replace('\n', '').replace('\t', '')
                content = json.loads(content)
            except:
                pass
            try:
                companyInfo = content['companyInfo']
            except:
                continue
            emissionInfo = content['emissionInfo']
            report_time = companyInfo['reportTime'].replace('第0', '第').replace('年0', '年')

            for emission, group in emissionInfo.items():
                emissionInfo[emission] = pd.DataFrame(group)

            dct[report_time] = emissionInfo

        try:
            if table:
                exception[f'{ent_id}({ent_name})'] = check_tbl(start, end, dct, whole=True)
            else:
                exception[f'{ent_id}({ent_name})'] = check_tbl(start, end, dct, whole=False)
        except:
            logger.error(f'error in {ent_id}({ent_name})')
            raise

    clean_null(exception)
    return exception


if __name__ == "__main__":
    # base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年重点企业执行报告'
    # # res = check_companies_same_val(base_path)
    # res = {'重庆泰濠制药有限公司': {('执行报告-重庆泰濠制药有限公司-2022年第01季.docx', '执行报告-重庆泰濠制药有限公司-2022年第02季.docx'): {'废气': {('其他排放（合计）', '其他排放（合计）', '二氯甲烷', 3e-06)}}, ('执行报告-重庆泰濠制药有限公司-2022年第03季.docx', '执行报告-重庆泰濠制药有限公司-2022年第04季.docx'): {'废气': {('其他排放（合计）', '其他排放（合计）', '非甲烷总烃', 342.3), ('其他排放（合计）', '其他排放（合计）', '臭气浓度', 480.0)}}}, '重庆和友实业股份有限公司': {('执行报告-重庆和友实业股份有限公司-2022年04月.docx', '执行报告-重庆和友实业股份有限公司-2022年05月.docx'): {'废水': {('全厂直接排放', '全厂直接排放', '全厂直接排放', '硫化物', 0.0002), ('主要排放口', '直接排放口', 'DW001', '挥发酚', 0.0003), ('全厂直接排放', '全厂直接排放', '全厂直接排放', '挥发酚', 0.0003), ('主要排放口', '直接排放口', 'DW001', '硫化物', 0.0002)}}}, '重庆铝王铝业有限公司': {('执行报告-重庆铝王铝业有限公司-2022年第01季.docx', '执行报告-重庆铝王铝业有限公司-2022年第02季.docx'): {'废水': {('全厂间接排放', '全厂间接排放', '全厂间接排放', '总氮（以N计）', 0.0008), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '氟化物（以F-计）', 0.00012), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '石油类', 0.0012), ('主要排放口', '间接排放口', 'DW002', '总氮（以N计）', 0.0008), ('主要排放口', '间接排放口', 'DW002', '氟化物（以F-计）', 0.00012), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '悬浮物', 6e-05), ('主要排放口', '间接排放口', 'DW002', '悬浮物', 6e-05), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '五日生化需氧量', 1.2e-05), ('主要排放口', '间接排放口', 'DW002', '五日生化需氧量', 1.2e-05), ('主要排放口', '间接排放口', 'DW002', '石油类', 0.0012)}}, ('执行报告-重庆铝王铝业有限公司-2022年第02季.docx', '执行报告-重庆铝王铝业有限公司-2022年第03季.docx'): {'废气': {('其他排放（合计）', '其他排放（合计）', '挥发性有机物', 0.009), ('全厂合计', '全厂合计', 'VOCs', 0.009)}, '废水': {('全厂间接排放', '全厂间接排放', '全厂间接排放', '氟化物（以F-计）', 0.00012), ('主要排放口', '间接排放口', 'DW002', '氟化物（以F-计）', 0.00012), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '悬浮物', 6e-05), ('全厂间接排放', '全厂间接排放', '全厂间接排放', 'pH值', 3.6), ('主要排放口', '间接排放口', 'DW002', '悬浮物', 6e-05), ('主要排放口', '间接排放口', 'DW002', 'pH值', 7.2), ('全厂间接排放', '全厂间接排放', '全厂间接排放', '五日生化需氧量', 1.2e-05), ('主要排放口', '间接排放口', 'DW002', '五日生化需氧量', 1.2e-05)}}}, '重庆西彭污水处理有限公司': {('执行报告-重庆西彭污水处理有限公司-2022年第03季.docx', '执行报告-重庆西彭污水处理有限公司-2022年第04季.docx'): {'废水': {('全厂直接排放', '全厂直接排放', '全厂直接排放', '总砷', 0.0005), ('主要排放口', '直接排放口', 'DW001', '总砷', 0.0005)}}}}
    # print(''.join(print_format(res)))
    exception = dbbase_run('九龙坡区', '2021-01-21', '2022-12-25', table=False)
    print(exception)