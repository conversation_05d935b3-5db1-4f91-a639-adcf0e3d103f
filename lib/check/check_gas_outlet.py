# -*- coding: utf-8 -*-
"""
@File  : check_gas_outlet.py
@Author: <PERSON>
@Date  : 2025/1/17 15:07
@Desc  : 许可证记载的污染物排放口信息与环境影响评价报告不符
"""
import pandas as pd
import os
import pickle

from lib import proj_dir
from lib.check.base import extract_tbl_from_tbl, listdir

pd.set_option('display.width', 300, 'display.max_columns', 10, 'display.max_rows', 50)


def extract_ent_outlet_tbls(file_path):
    """ 从环评报告中提取企业排放口信息表 """
    tbls = extract_tbl_from_tbl(file_path)
    outlets = []
    for name, tbl in tbls.items():
        tbl = tbl.fillna('')
        content = '\n'.join(
            ','.join(item).replace('\n', '')
            for _, item in tbl.iterrows()
        )
        if (name
            and ('排放' in name or '排气' in name)
            and ('内径' in content and '高度' in content)
        ):
            outlets.append(tbl)
    return outlets


def extract_ents_outlet_tbls(path):
    """ 提取所有企业环评报告中的排放口信息 """
    ents = sorted([item for item in listdir(path) if os.path.isdir(os.path.join(path, item))])
    dct = {}
    for ent in ents:
        ent_path = os.path.join(path, ent)
        file_names = listdir(ent_path)
        for file_name in file_names:
            if file_name.endswith('.docx'):
                file_path = os.path.join(ent_path, file_name)
                outlets = extract_ent_outlet_tbls(file_path)
                if outlets:
                    dct[ent] = outlets
                else:
                    print(f'文件 “{ent} / {file_name}” 中没有找到大气排放口表')
    return dct


def extract_info_from_tbl(data):
    """ 从排放口表中提取排放口数据 """
    dct = {}
    for name, tbls in data.items():
        lst = []
        for tbl in tbls:
            succeed = False
            for _, row in tbl.iterrows():
                _ks = [None, None, None]
                for k, v in row.items():
                    if '编号' in v or '名称' in v or '排放源' in v:
                        if _ks[0] is not None and '编号' not in v[0]:
                            pass
                        else:
                            _ks[0] = k
                    elif '高度' in v and '海拔' not in v:
                        _ks[1] = k
                    elif '内径' in v:
                        _ks[2] = k
                    if None not in _ks:
                        succeed = True
                        ks =_ks.copy()
                    if succeed and None in _ks:
                        height, diameter = row[ks[1]], row[ks[2]]
                        if isinstance(height, str):
                            height = height.replace('m', '').replace('>', '-')
                        if isinstance(diameter, str):
                            diameter = diameter.replace('m', '').replace('>', '-')
                        try:
                            _dct = {
                                'name': row[ks[0]],
                                'height': float(height),
                                'diameter': float(diameter)
                            }
                        except:
                            continue
                        if _dct not in lst:
                            lst.append(_dct)
        if lst:
            dct[name] = pd.DataFrame(lst)
        else:
            print(f'企业 “{name}” 废弃排放口表中没有提取到排口信息.')

    return dct


def filter_tbls(res):
    """ 只保留每个环评报告中最长的一个排气筒信息表 """
    for name, tbls in res.items():
        max_len = max([len(item) for item in tbls])
        res[name] = [item for item in tbls if len(item) == max_len]
    return res


if __name__ == '__main__':
    base_path = os.path.join(proj_dir, '材料/2022年环评项目电子档案资料')
    # res = extract_ents_outlet_tbls(base_path)
    # pickle.dump(res, open('outlets.pkl', 'wb'))
    res = pickle.load(open('outlets.pkl', 'rb'))
    for k, v in res.items():
        print(k)
        if len(v) == 1:
            v = v[0]
        print(v)
        print('='*30)
    print('=' * 50)
    res = filter_tbls(res)
    dct = extract_info_from_tbl(res)
    for k, v in dct.items():
        print(k)
        print(v)
        print('='*30)
