# -*- coding: utf-8 -*-
"""
@File  : check_reports.py
@Author: <PERSON>
@Date  : 2024/12/3 16:28
@Desc  : 月度、季度、年度执行报告污染物排放量不匹配
"""
import os
import traceback

import pandas as pd
from tqdm import tqdm
import json
from collections import defaultdict

from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx
from lib import FREQ_REQUIRED, logger
from lib.check.base import clean_null, get_exe_data
from lib.check.base import *


def absent_reports(base_path):
    """ 侦测未按照规定正常提交执行报告的情况 """
    ents = [i for i in os.listdir(base_path) if i in FREQ_REQUIRED]
    absence = {}
    for ent in ents:
        ent_path = os.path.join(base_path, ent)
        docs = os.listdir(ent_path)
        have_time = {doc.split('-')[-1][:-5] for doc in docs}
        # if not any(doc.endswith('月') for doc in have_time):
        #     have_time.update({f'2022年{i:02d}月' for i in range(1,13)})
        dct = {
            '2022年第01季': '2022年03月',
            '2022年第02季': '2022年06月',
            '2022年第03季': '2022年09月',
            '2022年第04季': '2022年12月',
            '2022年': '2022年第04季'
        }
        for have, ignore in dct.items():
            if have in have_time:
                have_time.add(ignore)

        # if not any(doc.endswith('季') for doc in have_time):
        #     have_time.update({f'2022年第{i:02d}季' for i in range(1,5)})
        # if '2022年' in have_time:
        #     have_time.add('2022年第04季')
        full_time = []
        if '月报' in FREQ_REQUIRED[ent]:
            full_time.extend([f'2022年{i:02d}月' for i in range(1,13)])
        if '季报' in FREQ_REQUIRED[ent]:
            full_time.extend([f'2022年第{i:02d}季' for i in range(1,5)])
        if '年报' in FREQ_REQUIRED[ent]:
            full_time.append('2022年')
        # full_time = [f'2022年{i:02d}月' for i in range(1,13)] + [f'2022年第{i:02d}季' for i in range(1,5)] + ['2022年']
        absence_time = set(full_time) - have_time
        if absence_time:
            absence[ent] = list(absence_time)
    return absence


### 月度、季度、年度执行报告污染物排放量不匹配
def check_ent(base_path):
    """ 核查单个企业的月度、季度、年度执行报告污染物排放量是否匹配 """
    docs = [item for item in os.listdir(base_path) if not item.startswith('~$')]
    prefix = '-'.join(docs[0].split('-')[:-1])
    ent = os.path.basename(base_path)
    times = [doc.split('-')[-1][:-5] for doc in docs]

    def read_docx_tbl(doc_path):
        path = os.path.join(base_path, doc_path)
        docx = MarkdownConverterWithPythonDocx(path)
        tables = docx.tables(format_='dataframe')
        gas = tables['1、实际排放量指报告执行期内实际排放量']
        water = tables['注：实际排放量指报告执行期内实际排放量']
        return gas, water

    def check_month(month_doc, sum_doc, _type):
        month_gas, month_water = read_docx_tbl(month_doc)
        sum_gas, sum_water = read_docx_tbl(sum_doc)
        month = str(int(month_doc[-8:-6])) + '月'

        # 设置表头
        month_gas = pd.DataFrame(month_gas[1:].values, columns=month_gas.iloc[0].tolist())
        month_water = pd.DataFrame(month_water[1:].values, columns=month_water.iloc[0].tolist())
        sum_gas = pd.DataFrame(sum_gas[2:].values, columns=sum_gas.iloc[1].tolist())
        sum_water = pd.DataFrame(sum_water[2:].values, columns=sum_water.iloc[1].tolist())
        month_col = '月报'
        month_gas.rename(columns={"实际排放量（吨）": month_col}, inplace=True)
        month_water.rename(columns={"实际排放量（吨）": month_col}, inplace=True)

        gas_cols = ['排放口类型', '排放口编码及名称', '污染物']
        water_cols = ['排放口类型', '排放方式', '排放口编码及名称', '污染物']
        month_gas_cols = gas_cols + [month_col]
        sum_gas_cols = gas_cols + [month]
        month_water_cols = water_cols + [month_col]
        sum_water_cols = water_cols + [month]
        month_gas = month_gas[month_gas_cols]
        sum_gas = sum_gas[sum_gas_cols]
        month_water = month_water[month_water_cols]
        sum_water = sum_water[sum_water_cols]

        gas = pd.merge(month_gas, sum_gas, how='inner', on=gas_cols)
        g_errors = gas[(gas[month_col]!=gas[month]) & (gas[month_col]!='/')].copy()
        water = pd.merge(month_water, sum_water, how='inner', on=water_cols)
        w_errors = water[(water[month_col]!=water[month]) & (water[month_col]!='/')].copy()

        for errors in [g_errors, w_errors]:
            errors['时间'] = month
            if _type == 'quarter':
                errors.rename(columns={month: '季报'}, inplace=True)
            elif _type == 'year':
                errors.rename(columns={month: '年报'}, inplace=True)

        records = {'gas': g_errors.to_dict('records'), 'water': w_errors.to_dict('records')}
        return records

    def check_quarter_year(quarter_doc, year_doc):
        quarter_gas, quarter_water = read_docx_tbl(quarter_doc)
        year_gas, year_water = read_docx_tbl(year_doc)
        quarter = str(int(quarter_doc[-8:-6])) + '季度'
        ent = os.path.basename(base_path)

        # 设置表头
        quarter_gas = pd.DataFrame(quarter_gas[2:].values, columns=quarter_gas.iloc[1].tolist())
        quarter_water = pd.DataFrame(quarter_water[2:].values, columns=quarter_water.iloc[1].tolist())
        year_gas = pd.DataFrame(year_gas[2:].values, columns=year_gas.iloc[1].tolist())
        year_water = pd.DataFrame(year_water[2:].values, columns=year_water.iloc[1].tolist())

        gas_cols = ['排放口类型', '排放口编码及名称', '污染物']
        water_cols = ['排放口类型', '排放方式', '排放口编码及名称', '污染物']
        quarter_gas_cols = gas_cols + ['季度合计']
        year_gas_cols = gas_cols + [quarter]
        quarter_water_cols = water_cols + ['季度合计']
        year_water_cols = water_cols + [quarter]
        quarter_gas = quarter_gas[quarter_gas_cols]
        quarter_water = quarter_water[quarter_water_cols]
        year_gas = year_gas[year_gas_cols]
        year_water = year_water[year_water_cols]

        gas = pd.merge(quarter_gas, year_gas, how='inner', on=gas_cols)
        g_errors = gas[(gas['季度合计']!=gas[quarter]) & (gas['季度合计']!='/')].copy()
        water = pd.merge(quarter_water, year_water, how='inner', on=water_cols)
        w_errors = water[(water['季度合计']!=water[quarter]) & (water['季度合计']!='/')].copy()

        for errors in [g_errors, w_errors]:
            errors.rename(columns={quarter: '年报', '季度合计': '季报'}, inplace=True)
            errors['时间'] = quarter

        records = {'gas': g_errors.to_dict('records'), 'water': w_errors.to_dict('records')}
        return records

    errors = defaultdict(dict)
    # 月度报告匹配判断
    months = [item for item in times if '月' in item]
    for month in tqdm(months, desc=ent+'   month'):
        quarter = (int(month[5:7])-1) // 3 + 1
        quarter = f'{month[:4]}年第{quarter:02d}季'
        year = f'{month[:4]}年'
        month = f'{prefix}-{month}.docx'
        quarter = f'{prefix}-{quarter}.docx'
        year = f'{prefix}-{year}.docx'
        if quarter:
            error = check_month(month, quarter, _type='quarter')
            for _type, group in error.items():
                if group:
                    errors['month-quarter'][_type] = errors['month-quarter'].get(_type, [])
                    errors['month-quarter'][_type].extend(group)
        if year:
            error = check_month(month, year, _type='year')
            for _type, group in error.items():
                if group:
                    errors['month-year'][_type] = errors['month-year'].get(_type, [])
                    errors['month-year'][_type].extend(group)

    # 季度报告匹配判断
    quarters = [item for item in times if '季' in item]
    for quarter in tqdm(quarters, desc=ent+' quarter'):
        year = f'{quarter[:4]}年'
        quarter = f'{prefix}-{quarter}.docx'
        year = f'{prefix}-{year}.docx'
        if year:
            error = check_quarter_year(quarter, year)
            for _type, group in error.items():
                if group:
                    errors['quarter-year'][_type] = errors['quarter-year'].get(_type, [])
                    errors['quarter-year'][_type].extend(group)

    return errors


def check_ents(base_path):
    errors = {}
    ents = os.listdir(base_path)
    for ent in ents:
        ent_path = os.path.join(base_path, ent)
        if os.path.isdir(ent_path):
            error = check_ent(ent_path)
            if error:
                errors[ent] = error
    return errors


def online_check_ents(city, start, end):

    def filter_tbl(tbl, col1, col2):
        alter = tbl[
            (tbl[col1].notnull()) & (tbl[col2].notnull()) &
            (tbl[col1] != '/') & (tbl[col2] != '/') & (tbl[col1] != tbl[col2])
        ].copy()
        return alter


    def pipeline1(m_data, q_data, join_cols, full_month, q_val_col, category, exception):
        """ 月报-季报校验流水线 """
        merge = pd.merge(m_data, q_data, how='left', on=join_cols)
        alter = filter_tbl(merge, 'emitValue', q_val_col)
        alter.rename(columns={'emitValue': 'month', q_val_col: 'quarter'}, inplace=True)
        alter['time'] = full_month
        alter = alter[join_cols + ['month', 'quarter', 'time']]
        exception['month-quarter'][category].extend(alter.to_dict(orient='records'))
        return exception


    def pipeline2(m_data, y_data, join_cols, full_month, y_val_col, category, exception):
        """ 月报-年报校验流水线 """
        merge = pd.merge(m_data, y_data, how='left', on=join_cols)
        alter = filter_tbl(merge, 'emitValue', y_val_col)
        alter.rename(columns={'emitValue': 'month', y_val_col: 'year'}, inplace=True)
        alter['time'] = full_month
        alter = alter[join_cols + ['month', 'year', 'time']]
        exception['month-year'][category].extend(alter.to_dict(orient='records'))
        return exception


    def pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, category, exception):
        """ 季报-年报校验流水线 """
        merge = pd.merge(q_data, y_data, how='left', on=join_cols)
        alter = filter_tbl(merge, q_val_col, y_val_col)
        alter.rename(columns={q_val_col: 'quarter', y_val_col: 'year'}, inplace=True)
        alter['time'] = full_quarter
        alter = alter[join_cols + ['quarter', 'year', 'time']]
        exception['quarter-year'][category].extend(alter.to_dict(orient='records'))
        return exception


    def check_ent(start, end, ent_data):
        start_year, end_year = int(start[:4]), int(end[:4])
        start_month, end_month = int(start.split('-')[1]), int(end.split('-')[1])
        start_quarter, end_quarter = (end_month+2) // 3, (end_month+2) // 3
        exception = {
            'month-quarter': {'gas': [], 'water': []},
            'month-year': {'gas': [], 'water': []},
            'quarter-year': {'gas': [], 'water': []}
        }

        # 月报校验
        for year in range(start_year, end_year+1):
            for month in range(1, 13):
                if year == start_year and month < start_month:
                    continue
                if year == end_year and month > end_month:
                    continue

                # 月报不存在
                full_month = f'{year}年{month}月'
                if full_month not in ent_data:
                    continue

                month_data = ent_data[full_month]

                # 月报-季报校验
                quarter = (month-1) // 3 + 1
                idx_inner_quarter = (month % 3) or 3
                q_val_col = mq_idx2col[idx_inner_quarter]
                full_quarter = f'{year}年第{quarter}季'
                if full_quarter in ent_data:
                    quarter_data = ent_data[full_quarter]
                    for emission, m_data in month_data.items():
                        if emission not in quarter_data or quarter_data[emission].empty or m_data.empty:
                            continue
                        q_emisson_data = quarter_data[emission]
                        if 'pollutantCode' not in m_data:
                            m_data['pollutantCode'] = ''
                        if emission == 'airMainEmission':
                            for col in ['outletName', 'outletCode']:
                                if col not in m_data:
                                    m_data[col] = ''
                            m_data = m_data[m_airmain_cols]
                            q_data = q_emisson_data[q_airmain_cols]
                            join_cols = ['portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'gas', exception)
                        elif emission == 'airMinorEmission':
                            m_data = m_data[m_airminor_cols]
                            q_data = q_emisson_data[q_airminor_cols]
                            join_cols = ['portType', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'gas', exception)
                        elif emission == 'airTotalEmission':
                            m_data = m_data[m_airtotal_cols]
                            q_data = q_emisson_data[q_airtotal_cols]
                            join_cols = ['portType', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'gas', exception)
                        elif emission == 'waterMainEmission':
                            for col in ['outletName', 'outletCode']:
                                if col not in m_data:
                                    m_data[col] = ''
                            m_data = m_data[m_watermain_cols]
                            q_data = q_emisson_data[q_watermain_cols]
                            join_cols = ['portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'water', exception)
                        elif emission == 'waterMinorEmission':
                            m_data = m_data[m_waterminor_cols]
                            q_data = q_emisson_data[q_waterminor_cols]
                            join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'water', exception)
                        elif emission == 'waterTotalEmission':
                            m_data = m_data[m_watertotal_cols]
                            q_data = q_emisson_data[q_watertotal_cols]
                            join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                            pipeline1(m_data, q_data, join_cols, full_month, q_val_col, 'water', exception)

                # 月报-年报校验
                full_year = f'{year}年'
                y_val_col = my_idx2col[month]
                if full_year in ent_data:
                    year_data = ent_data[full_year]
                    for emission, m_data in month_data.items():
                        if emission not in year_data or year_data[emission].empty or m_data.empty:
                            continue
                        y_emission_data = year_data[emission]
                        if 'pollutantCode' not in m_data:
                            m_data['pollutantCode'] = ''
                        if emission == 'airMainEmission':
                            for col in ['outletName', 'outletCode']:
                                if col not in m_data:
                                    m_data[col] = ''
                            m_data = m_data[m_airmain_cols]
                            y_data = y_emission_data[y_airmain_cols]
                            join_cols = ['portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'gas', exception)
                        elif emission == 'airMinorEmission':
                            m_data = m_data[m_airminor_cols]
                            y_data = y_emission_data[y_airminor_cols]
                            join_cols = ['portType', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'gas', exception)
                        elif emission == 'airTotalEmission':
                            m_data = m_data[m_airtotal_cols]
                            y_data = y_emission_data[y_airtotal_cols]
                            join_cols = ['portType', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'gas', exception)
                        elif emission == 'waterMainEmission':
                            for col in ['outletName', 'outletCode']:
                                if col not in m_data:
                                    m_data[col] = ''
                            m_data = m_data[m_watermain_cols]
                            y_data = y_emission_data[y_watermain_cols]
                            join_cols = ['portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'water', exception)
                        elif emission == 'waterMinorEmission':
                            m_data = m_data[m_waterminor_cols]
                            y_data = y_emission_data[y_waterminor_cols]
                            join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'water', exception)
                        elif emission == 'waterTotalEmission':
                            m_data = m_data[m_watertotal_cols]
                            y_data = y_emission_data[y_watertotal_cols]
                            join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                            pipeline2(m_data, y_data, join_cols, full_month, y_val_col, 'water', exception)

        # 季报校验
        for year in range(start_year, end_year + 1):
            for quarter in range(1, 5):
                if year == start_year and quarter < start_quarter:
                    continue
                if year == end_year and quarter > end_quarter:
                    continue

                # 季报不存在
                full_quarter = f'{year}年第{quarter}季'
                if full_quarter not in ent_data:
                    continue

                quarter_data = ent_data[full_quarter]

                idx_inner_quarter = (month % 3) or 3
                q_val_col = 'total'
                y_val_col = qy_idx2col[quarter]
                full_year = f'{year}年'
                if full_year not in ent_data:
                    break
                year_data = ent_data[full_year]
                for emission, q_data in quarter_data.items():
                    if emission not in year_data or year_data[emission].empty or q_data.empty:
                        continue
                    y_emission_data = year_data[emission]
                    if emission == 'airMainEmission':
                        if 'outletCode' not in q_data:
                            q_data['outletCode'] = ''
                        q_data = q_data[q_airmain_cols]
                        y_data = y_emission_data[y_airmain_cols]
                        join_cols = ['portType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'gas', exception)
                    elif emission == 'airMinorEmission':
                        q_data = q_data[q_airminor_cols]
                        y_data = y_emission_data[y_airminor_cols]
                        join_cols = ['portType', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'gas', exception)
                    elif emission == 'airTotalEmission':
                        q_data = q_data[q_airtotal_cols]
                        y_data = y_emission_data[y_airtotal_cols]
                        join_cols = ['portType', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'gas', exception)
                    elif emission == 'waterMainEmission':
                        if 'outletCode' not in q_data:
                            q_data['outletCode'] = ''
                        q_data = q_data[q_watermain_cols]
                        y_data = y_emission_data[y_watermain_cols]
                        join_cols = ['portType', 'emissionType', 'outletName', 'outletCode', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'water', exception)
                    elif emission == 'waterMinorEmission':
                        q_data = q_data[q_waterminor_cols]
                        y_data = y_emission_data[y_waterminor_cols]
                        join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'water', exception)
                    elif emission == 'waterTotalEmission':
                        q_data = q_data[q_watertotal_cols]
                        y_data = y_emission_data[y_watertotal_cols]
                        join_cols = ['portType', 'emissionType', 'pollutantName', 'pollutantCode']
                        pipeline3(q_data, y_data, join_cols, full_quarter, q_val_col, y_val_col, 'water', exception)

        return exception


    data = get_exe_data(city)
    exception = defaultdict(dict)
    for (ent_id, ent_name), group in data.groupby(['f_enterprise_id', 'f_enterprise_name']):
        dct = defaultdict(dict)
        for _, row in group.iterrows():
            time = row['f_report_time']
            content = row['f_report_content']
            try:
                content = json.loads(content)
            except:
                pass
            content = content['data']
            try:
                if isinstance(content, str):
                    content = content.replace('\n', '').replace('\t', '')
                content = json.loads(content)
            except:
                pass
            try:
                companyInfo = content['companyInfo']
            except:
                logger.error(f'提取 {ent_id}({ent_name}) 时出错：\n'+str(traceback.format_exc()))
                continue
            emissionInfo = content['emissionInfo']
            report_time = companyInfo['reportTime'].replace('第0', '第').replace('年0', '年')

            for emission, group in emissionInfo.items():
                emissionInfo[emission] = pd.DataFrame(group)

            dct[report_time] = emissionInfo

        try:
            exception[f'{ent_id}({ent_name})'] = check_ent(start, end, dct)
        except:
            logger.error(f'error in {ent_id}({ent_name})')
            raise

    clean_null(exception)
    return exception


if __name__ == '__main__':
    # base_path = '/home/<USER>/project/large_model/srv-jiulongpo-word-check/材料/2022年重点企业执行报告'
    # res = absent_reports(base_path)
    # print_format(res)
    # res = check_ents(base_path)
    # print_format(res)
    # save_json(os.path.join(proj_dir, 'data/report_errors.json'), res)

    # base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年重点企业执行报告/重庆和友实业股份有限公司'
    # res = check_ent(base_path)
    # print_format(res)

    exception = online_check_ents('呼和浩特市', '2021-02-21','2022-10-10')
    for k, v in exception.items():
        print(k)
        print(v)
        print('\n')