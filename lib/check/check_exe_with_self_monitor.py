# -*- coding: utf-8 -*-
"""
@File  : check_exe_with_self_monitor.py
@Author: <PERSON>
@Date  : 2025/7/27
@Desc  : 执行报告与自行监测数据校验程序
         比较同一企业、同一排口、同一污染因子的监测数据是否匹配

异常记录字段说明（合并后的数据结构）：
- pollutant_type: 污染物类型，'air'(废气) 或 'water'(废水)
- f_enterprise_id: 执行报告中的企业ID，用于唯一标识企业
- f_enterprise_name: 执行报告中的企业名称，便于人工识别
- std_code: 污染物标准编码，国家统一的污染物编码标准
- outlet_id: 排放口编号，标识具体的排放口位置
- monitoring_site_data: 监测点位数据，具体的监测位置描述
- monitoring_indicator_data: 监测指标数据（污染因子），如SO2、NOx等
- report_year: 执行报告年份，来源于report_time字段
- count: 有效监测数据数量不匹配信息，存在异常时包含exec_value、self_value、difference，否则为空字典{}
- max: 最大值不匹配信息，存在异常时包含exec_value、self_value、difference，否则为空字典{}
- min: 最小值不匹配信息，存在异常时包含exec_value、self_value、difference，否则为空字典{}
- avg: 平均值不匹配信息，存在异常时包含exec_value、self_value、difference，否则为空字典{}

每个子对象（count、max、min、avg）在存在异常时包含：
- exec_value: 执行报告中的值
- self_value: 自行监测统计的值
- difference: 两值之间的差异（绝对值）
"""
import pandas as pd
import numpy as np
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import time

from lib import logger
from lib.check.base import get_db_cursor, timing_decorator


# 全局缓存
_CACHE = {
    'city_codes': {},
    'enterprise_mappings': {},
    'last_update': {}
}

# 配置参数
CONFIG = {
    'tolerance': 0.05,  # 5%容差
    'avg_absolute_tolerance': 0.1,  # 平均值绝对容差
    'cache_timeout': 600,  # 缓存超时时间（秒）
    'batch_size': 1000  # 批处理大小
}

# 性能监控
_PERFORMANCE_STATS = {
    'total_runs': 0,
    'total_time': 0.0,
    'avg_time': 0.0,
    'last_run_time': 0.0,
    'data_processed': {
        'exec_gas_records': 0,
        'exec_water_records': 0,
        'self_gas_records': 0,
        'self_water_records': 0
    },
    'anomalies_found': {
        'total': 0,
        'by_type': defaultdict(int)
    }
}


def convert_numeric_columns(df: pd.DataFrame, columns: List[str], data_source: str = "") -> pd.DataFrame:
    """
    安全地将DataFrame中指定列转换为float64类型

    Args:
        df (pd.DataFrame): 要转换的DataFrame
        columns (List[str]): 需要转换的列名列表
        data_source (str): 数据源描述，用于日志记录

    Returns:
        pd.DataFrame: 转换后的DataFrame
    """
    if df.empty:
        return df

    df_converted = df.copy()
    conversion_stats = {
        'total_columns': len(columns),
        'successful_conversions': 0,
        'failed_conversions': 0,
        'details': []
    }

    for col in columns:
        if col not in df_converted.columns:
            logger.debug(f'列 {col} 不存在于{data_source}数据中，跳过转换')
            continue

        try:
            # 记录转换前的数据类型
            original_dtype = df_converted[col].dtype

            # 使用pd.to_numeric进行安全转换
            df_converted[col] = pd.to_numeric(df_converted[col], errors='coerce')

            # 转换为float64类型
            df_converted[col] = df_converted[col].astype('float64')

            # 统计转换结果
            conversion_stats['successful_conversions'] += 1
            conversion_stats['details'].append({
                'column': col,
                'original_dtype': str(original_dtype),
                'new_dtype': 'float64',
                'status': 'success'
            })

            logger.debug(f'{data_source}列 {col} 类型转换成功: {original_dtype} -> float64')

        except Exception as e:
            conversion_stats['failed_conversions'] += 1
            conversion_stats['details'].append({
                'column': col,
                'original_dtype': str(df_converted[col].dtype),
                'error': str(e),
                'status': 'failed'
            })

            logger.warning(f'{data_source}列 {col} 类型转换失败: {str(e)}')

    return df_converted


def get_city_code(city: str) -> str:
    """
    根据城市名称获取区域编码（带缓存）

    Args:
        city (str): 城市名称

    Returns:
        str: 城市编码
    """
    # 检查缓存
    current_time = time.time()
    cache_key = f'city_code_{city}'

    if (cache_key in _CACHE['city_codes'] and
        cache_key in _CACHE['last_update'] and
        current_time - _CACHE['last_update'][cache_key] < CONFIG['cache_timeout']):
        logger.debug(f'从缓存获取城市{city}的区域编码')
        return _CACHE['city_codes'][cache_key]

    logger.info(f'查询城市{city}的区域编码...')

    db = get_db_cursor('main')

    sql = """
        SELECT city_code FROM dim_ref_comm_prov_city
        WHERE city_name = '{city}'
    """.format(city=city)

    result = db.query_sql(sql)
    db.close()

    if result.empty:
        raise ValueError(f'未找到城市{city}的区域编码')

    city_code = result.iloc[0]['city_code']

    # 更新缓存
    _CACHE['city_codes'][cache_key] = city_code
    _CACHE['last_update'][cache_key] = current_time

    logger.info(f'城市{city}的区域编码为: {city_code}')

    return city_code


def get_enterprise_mapping(city_code: str) -> pd.DataFrame:
    """
    获取企业关联映射表数据（带缓存）

    Args:
        city_code (str): 城市编码

    Returns:
        pd.DataFrame: 企业关联映射数据
    """
    # 检查缓存
    current_time = time.time()
    cache_key = f'enterprise_mapping_{city_code}'

    if (cache_key in _CACHE['enterprise_mappings'] and
        cache_key in _CACHE['last_update'] and
        current_time - _CACHE['last_update'][cache_key] < CONFIG['cache_timeout']):
        logger.debug(f'从缓存获取区域{city_code}的企业关联映射数据')
        return _CACHE['enterprise_mappings'][cache_key]

    logger.info(f'获取区域{city_code}的企业关联映射数据...')

    db = get_db_cursor('main')

    # 动态生成表名（使用city_code作为城市标识符）
    from lib.check.table_name_generator import get_table_name
    mapping_table = get_table_name("basic_information_enterprise_map", city_code)

    sql = f"""
        SELECT f_enterprise_id, self_id, f_enterprise_name, enterprise_name
        FROM {mapping_table}
        WHERE region_code_city = '{city_code}'
            AND f_enterprise_id IS NOT NULL
            AND self_id IS NOT NULL
    """

    mapping_data = db.query_sql(sql)
    db.close()

    # 更新缓存
    _CACHE['enterprise_mappings'][cache_key] = mapping_data.copy()
    _CACHE['last_update'][cache_key] = current_time

    logger.info(f'获取到{len(mapping_data)}条企业关联映射数据')

    return mapping_data


def extract_all_execution_report_data(city_code: str, years: Optional[List[int]] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    一次性提取废气和废水执行报告数据（支持年份筛选）

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表，如[2024, 2025]，基于report_time字段筛选

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (废气数据, 废水数据)
    """
    logger.info(f'提取区域{city_code}的执行报告数据（废气+废水）...')

    # 构建年份筛选条件 - 基于report_time字段（字符串类型）
    year_condition = ""

    if years:
        # 构建年份筛选条件，report_time是字符串类型，直接比较年份
        year_strings = [f"'{str(year)}'" for year in years]
        year_condition = f" AND report_time IN ({', '.join(year_strings)})"
        logger.info(f'年份筛选条件: {years}，基于report_time字段')

    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    gas_table = get_table_name("zx_basic_map_zxgl_self_monitoring_waste_gas", city_code)
    water_table = get_table_name("zx_basic_map_zxgl_self_monitoring_waste_water", city_code)

    # 废气执行报告数据
    gas_sql = f"""
        SELECT f_enterprise_id, f_enterprise_name, outlet_id,
               monitoring_site_data, monitoring_indicator_data, pollutant_name,
               valid_monitoring_data_count, hourly_con_min, hourly_con_max, hourly_con_avg,
               std_code, monitoring_equip, report_time,
               CAST(report_time AS INTEGER) AS report_year,
               'waste_gas' as data_type
        FROM {gas_table}
        WHERE region_code = '{city_code}' AND monitoring_equip = '手工'{year_condition}
    """

    # 废水执行报告数据
    water_sql = f"""
        SELECT f_enterprise_id, f_enterprise_name, outlet_id,
               monitoring_site_data, monitoring_indicator_data, pollutant_name,
               valid_monitoring_data_count, daily_con_min, daily_con_max, daily_con_avg,
               std_code, monitoring_equip, report_time,
               CAST(report_time AS INTEGER) AS report_year,
               'waste_water' as data_type
        FROM {water_table}
        WHERE region_code = '{city_code}' AND monitoring_equip = '手工'{year_condition}
    """

    # 使用字符串格式化，只传递SQL语句
    gas_data = db.query_sql(gas_sql)
    water_data = db.query_sql(water_sql)

    db.close()

    logger.info(f'提取到废气执行报告数据: {len(gas_data)} 条, 废水执行报告数据: {len(water_data)} 条')

    # 转换数值列为float64类型
    gas_numeric_columns = ['valid_monitoring_data_count', 'hourly_con_min', 'hourly_con_max', 'hourly_con_avg']
    water_numeric_columns = ['valid_monitoring_data_count', 'daily_con_min', 'daily_con_max', 'daily_con_avg']

    gas_data = convert_numeric_columns(gas_data, gas_numeric_columns, "废气执行报告")
    water_data = convert_numeric_columns(water_data, water_numeric_columns, "废水执行报告")

    return gas_data, water_data


def extract_execution_report_gas_data(city_code: str, years: Optional[List[int]] = None) -> pd.DataFrame:
    """
    提取废气执行报告数据（只取手工监测）

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表

    Returns:
        pd.DataFrame: 废气执行报告数据
    """
    gas_data, _ = extract_all_execution_report_data(city_code, years)
    return gas_data


def extract_execution_report_water_data(city_code: str, years: Optional[List[int]] = None) -> pd.DataFrame:
    """
    提取废水执行报告数据（只取手工监测）

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表

    Returns:
        pd.DataFrame: 废水执行报告数据
    """
    _, water_data = extract_all_execution_report_data(city_code, years)
    return water_data


def extract_all_self_monitoring_data(city_code: str, years: Optional[List[int]] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    一次性提取废气和废水自行监测数据（支持年份筛选）

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表，如[2024, 2025]，基于monitoring_time字段筛选

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (废气数据, 废水数据)
    """
    logger.info(f'提取区域{city_code}的自行监测数据（废气+废水）...')

    # 构建年份筛选条件 - 基于monitoring_time字段
    year_condition = ""

    if years:
        # 构建年份范围条件，使用字符串格式化
        year_conditions = []
        for year in years:
            # 每个年份构建一个范围条件，使用字符串格式化
            year_conditions.append(f"(monitoring_time >= '{year}-01-01' AND monitoring_time < '{year + 1}-01-01')")

        year_condition = f" AND ({' OR '.join(year_conditions)})"
        logger.info(f'年份筛选条件: {years}，基于monitoring_time字段')

    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    gas_monitoring_table = get_table_name("basic_data_waste_gas_monitoring", city_code)
    water_monitoring_table = get_table_name("basic_data_waste_water_monitoring", city_code)

    # 废气自行监测数据
    gas_sql = f"""
        SELECT self_id, enterprise_name, monitoring_site_data,
               monitoring_indicator_data, monitoring_result, monitoring_time,
               EXTRACT(YEAR FROM monitoring_time) AS monitoring_year,
               'waste_gas' as data_type
        FROM {gas_monitoring_table}
        WHERE region_code = '{city_code}'
            AND monitoring_result IS NOT NULL
            AND is_emission_rate = 0{year_condition}
    """

    # 废水自行监测数据
    water_sql = f"""
        SELECT self_id, enterprise_name, monitoring_site_data,
               monitoring_indicator_data, monitoring_result, monitoring_time,
               EXTRACT(YEAR FROM monitoring_time) AS monitoring_year,
               'waste_water' as data_type
        FROM {water_monitoring_table}
        WHERE region_code = '{city_code}'
            AND monitoring_result IS NOT NULL
            AND is_emission_rate = 0{year_condition}
    """

    # 使用字符串格式化，只传递SQL语句
    gas_data = db.query_sql(gas_sql)
    water_data = db.query_sql(water_sql)

    db.close()

    logger.info(f'提取到废气自行监测数据: {len(gas_data)} 条, 废水自行监测数据: {len(water_data)} 条')

    # 转换数值列为float64类型
    numeric_columns = ['monitoring_result', 'monitoring_year']

    gas_data = convert_numeric_columns(gas_data, numeric_columns, "废气自行监测")
    water_data = convert_numeric_columns(water_data, numeric_columns, "废水自行监测")

    return gas_data, water_data


def extract_self_monitoring_gas_data(city_code: str, years: Optional[List[int]] = None) -> pd.DataFrame:
    """
    提取废气自行监测数据（is_emission_rate=0且monitoring_result不为空）

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表

    Returns:
        pd.DataFrame: 废气自行监测数据
    """
    gas_data, _ = extract_all_self_monitoring_data(city_code, years)
    return gas_data


def extract_self_monitoring_water_data(city_code: str, years: Optional[List[int]] = None) -> pd.DataFrame:
    """
    提取废水自行监测数据（is_emission_rate=0且monitoring_result不为空）
    注意：废水数据需要先计算日平均值，因为执行报告是日均级别的统计值

    Args:
        city_code (str): 城市编码
        years (List[int], optional): 年份列表

    Returns:
        pd.DataFrame: 废水自行监测数据（已计算日平均值）
    """
    _, water_data = extract_all_self_monitoring_data(city_code, years)

    # 对废水数据计算日平均值
    if not water_data.empty:
        water_data = calculate_daily_average_for_water(water_data)

    return water_data


def calculate_ph_average(ph_values: pd.Series) -> float:
    """
    计算pH值的正确平均值

    使用特殊公式：
    1. 将pH值转换为氢离子浓度：10^(-pH)
    2. 计算氢离子浓度的算术平均值
    3. 对平均值取负对数：pH均值 = -log10(平均氢离子浓度)

    Args:
        ph_values (pd.Series): pH值序列

    Returns:
        float: 计算得到的pH平均值
    """
    # 过滤掉无效的pH值
    valid_ph_values = ph_values[
        pd.notna(ph_values) &
        (ph_values >= 0) &
        (ph_values <= 14)
    ]

    if valid_ph_values.empty:
        return np.nan

    try:
        # 转换为氢离子浓度：10^(-pH)
        h_concentrations = 10 ** (-valid_ph_values)

        # 计算氢离子浓度的算术平均值
        avg_h_concentration = h_concentrations.mean()

        # 防止log10(0)的情况
        if avg_h_concentration <= 0:
            logger.warning(f'氢离子浓度平均值异常: {avg_h_concentration}，使用原始pH值的算术平均值')
            return valid_ph_values.mean()

        # 计算pH均值：-log10(平均氢离子浓度)
        ph_avg = -np.log10(avg_h_concentration)

        # 验证结果的合理性
        if not (0 <= ph_avg <= 14):
            logger.warning(f'计算得到的pH均值超出合理范围: {ph_avg}，使用原始pH值的算术平均值')
            return valid_ph_values.mean()

        return ph_avg

    except Exception as e:
        logger.error(f'pH值平均值计算出错: {str(e)}，使用原始pH值的算术平均值')
        return valid_ph_values.mean()


def calculate_daily_average_for_water(water_data: pd.DataFrame) -> pd.DataFrame:
    """
    计算废水自行监测数据的日平均值

    因为执行报告废水数据是日均级别的统计值，所以自行监测废水数据需要先根据时间
    monitoring_time先计算日平均值，然后再进行统计分析

    特别处理：
    - 对于pH值（monitoring_indicator_data为"pH值"），使用特殊公式计算日均值：
      1. 将pH值转换为氢离子浓度：10^(-pH)
      2. 计算氢离子浓度的算术平均值
      3. 对平均值取负对数：pH均值 = -log10(平均氢离子浓度)
    - 对于其他污染因子，使用普通算术平均值

    Args:
        water_data (pd.DataFrame): 原始废水自行监测数据

    Returns:
        pd.DataFrame: 计算日平均值后的废水数据
    """
    logger.info('开始计算废水自行监测数据的日平均值...')

    if water_data.empty:
        return water_data

    # 确保monitoring_time是datetime类型
    water_data = water_data.copy()
    water_data['monitoring_time'] = pd.to_datetime(water_data['monitoring_time'])

    # 提取日期（年-月-日）
    water_data['monitoring_date'] = water_data['monitoring_time'].dt.date

    # 按企业、排口、污染因子、日期分组计算日平均值
    group_keys = ['self_id', 'enterprise_name', 'monitoring_site_data',
                  'monitoring_indicator_data', 'monitoring_date', 'monitoring_year', 'data_type']

    # 分别处理pH值和其他污染因子
    ph_data = water_data[water_data['monitoring_indicator_data'] == 'pH值'].copy()
    other_data = water_data[water_data['monitoring_indicator_data'] != 'pH值'].copy()

    daily_avg_list = []

    # 处理pH值数据
    if not ph_data.empty:
        logger.info(f'发现{len(ph_data)}条pH值数据，使用特殊公式计算日平均值...')

        def calculate_ph_daily_average(group):
            """计算pH值的日平均值，使用特殊公式"""
            ph_values = group['monitoring_result']
            ph_avg = calculate_ph_average(ph_values)

            return pd.Series({
                'monitoring_result': ph_avg,
                'monitoring_time': group['monitoring_time'].iloc[0]
            })

        ph_daily_avg = ph_data.groupby(group_keys).apply(calculate_ph_daily_average).reset_index()
        daily_avg_list.append(ph_daily_avg)

    # 处理其他污染因子数据
    if not other_data.empty:
        logger.info(f'处理{len(other_data)}条非pH值数据，使用普通算术平均值...')

        other_daily_avg = other_data.groupby(group_keys).agg({
            'monitoring_result': 'mean',  # 计算日平均值
            'monitoring_time': 'first'    # 保留第一个时间作为代表时间
        }).reset_index()
        daily_avg_list.append(other_daily_avg)

    # 合并所有日平均数据
    if daily_avg_list:
        daily_avg_data = pd.concat(daily_avg_list, ignore_index=True)
    else:
        daily_avg_data = pd.DataFrame()

    # 更新monitoring_time为日期的开始时间
    if not daily_avg_data.empty:
        daily_avg_data['monitoring_time'] = pd.to_datetime(daily_avg_data['monitoring_date'])

        # 删除临时的日期列
        daily_avg_data = daily_avg_data.drop('monitoring_date', axis=1)

    logger.info(f'废水日平均值计算完成：原始数据 {len(water_data)} 条 -> 日平均数据 {len(daily_avg_data)} 条')

    # 记录pH值处理统计
    if not ph_data.empty:
        ph_daily_count = len(daily_avg_data[daily_avg_data['monitoring_indicator_data'] == 'pH值'])
        logger.info(f'其中pH值数据：原始 {len(ph_data)} 条 -> 日平均 {ph_daily_count} 条')

    return daily_avg_data


def calculate_monitoring_stats(monitoring_data: pd.DataFrame, group_keys: List[str]) -> pd.DataFrame:
    """
    计算自行监测数据的统计值（数量、最大值、最小值、平均值）

    Args:
        monitoring_data (pd.DataFrame): 自行监测数据
        group_keys (list): 分组字段列表

    Returns:
        pd.DataFrame: 统计结果
    """
    logger.info('计算自行监测数据统计值...')

    if monitoring_data.empty:
        return pd.DataFrame()

    # 使用向量化操作一次性计算所有统计值
    def calculate_stats(group):
        values = group['monitoring_result']
        non_zero_values = values[values > 0]

        # 检查是否为pH值数据，如果是则使用特殊公式计算平均值
        if 'monitoring_indicator_data' in group.columns and \
           group['monitoring_indicator_data'].iloc[0] == 'pH值':
            # 对于pH值，使用特殊公式计算平均值
            ph_mean = calculate_ph_average(values)
            logger.debug(f'pH值年度统计：使用特殊公式计算平均值 = {ph_mean}')
        else:
            # 对于其他污染因子，使用普通算术平均值
            ph_mean = values.mean()

        return pd.Series({
            'count': len(values),
            'min': values.min(),
            'max': values.max(),
            'mean': ph_mean,
            'non_zero_min': non_zero_values.min() if not non_zero_values.empty else np.nan
        })

    # 分批处理大数据集
    if len(monitoring_data) > CONFIG['batch_size']:
        logger.info(f'数据量较大({len(monitoring_data)}条)，采用分批处理...')

        # 按分组键预排序以提高性能
        monitoring_data = monitoring_data.sort_values(group_keys)

        stats_list = []
        grouped = monitoring_data.groupby(group_keys)

        for name, group in grouped:
            group_stats = calculate_stats(group)
            group_stats_dict = dict(zip(group_keys, name if isinstance(name, tuple) else [name]))
            group_stats_dict.update(group_stats.to_dict())
            stats_list.append(group_stats_dict)

        stats = pd.DataFrame(stats_list)

        # 确保统计结果的数值列都是float64类型
        if not stats.empty:
            numeric_stats_columns = ['count', 'min', 'max', 'mean', 'non_zero_min']
            stats = convert_numeric_columns(stats, numeric_stats_columns, "统计结果(分批)")
    else:
        # 小数据集直接处理
        stats = monitoring_data.groupby(group_keys).apply(calculate_stats).reset_index()

    logger.info(f'计算得到{len(stats)}组统计数据')

    # 确保统计结果的数值列都是float64类型
    if not stats.empty:
        numeric_stats_columns = ['count', 'min', 'max', 'mean', 'non_zero_min']
        stats = convert_numeric_columns(stats, numeric_stats_columns, "统计结果")

    return stats


def compare_monitoring_data(exec_data, self_stats, data_type):
    """
    比较执行报告与自行监测统计数据

    Args:
        exec_data (pd.DataFrame): 执行报告数据
        self_stats (pd.DataFrame): 自行监测统计数据
        data_type (str): 数据类型 ('waste_gas' 或 'waste_water')

    Returns:
        list: 异常记录列表
    """
    logger.info(f'开始比较{data_type}数据...')

    if exec_data.empty or self_stats.empty:
        logger.warning(f'{data_type}数据为空，跳过比较')
        return []

    # 验证关键数值列的数据类型
    if data_type == 'waste_gas':
        exec_numeric_cols = ['hourly_con_min', 'hourly_con_max', 'hourly_con_avg', 'valid_monitoring_data_count']
    else:  # waste_water
        exec_numeric_cols = ['daily_con_min', 'daily_con_max', 'daily_con_avg', 'valid_monitoring_data_count']

    stats_numeric_cols = ['min', 'max', 'mean', 'count', 'non_zero_min']

    # 记录数据类型信息用于调试
    for col in exec_numeric_cols:
        if col in exec_data.columns:
            logger.debug(f'执行报告列 {col} 数据类型: {exec_data[col].dtype}')

    for col in stats_numeric_cols:
        if col in self_stats.columns:
            logger.debug(f'统计数据列 {col} 数据类型: {self_stats[col].dtype}')

    # 记录年份分布情况
    if 'report_year' in exec_data.columns:
        exec_years = exec_data['report_year'].unique()
        logger.info(f'执行报告数据包含年份: {sorted(exec_years)}')

    if 'monitoring_year' in self_stats.columns:
        self_years = self_stats['monitoring_year'].unique()
        logger.info(f'自行监测数据包含年份: {sorted(self_years)}')

    anomalies = []

    # 根据数据类型选择对应的字段名
    if data_type == 'waste_gas':
        min_col = 'hourly_con_min'
        max_col = 'hourly_con_max'
        avg_col = 'hourly_con_avg'
    else:  # waste_water
        min_col = 'daily_con_min'
        max_col = 'daily_con_max'
        avg_col = 'daily_con_avg'

    # 遍历执行报告数据
    for _, exec_row in exec_data.iterrows():
        # 查找对应的自行监测统计数据（必须是同一年份）
        matching_stats = self_stats[
            (self_stats['self_id'] == exec_row.get('self_id', '')) &
            (self_stats['monitoring_site_data'] == exec_row['monitoring_site_data']) &
            (self_stats['monitoring_indicator_data'] == exec_row['monitoring_indicator_data']) &
            (self_stats['monitoring_year'] == exec_row['report_year'])
        ]

        if matching_stats.empty:
            # 记录未找到匹配数据的情况（用于调试）
            logger.debug(f'未找到匹配的自行监测数据: 企业={exec_row["f_enterprise_name"]}, '
                        f'排口={exec_row["monitoring_site_data"]}, '
                        f'污染因子={exec_row["monitoring_indicator_data"]}, '
                        f'年份={exec_row["report_year"]}')
            continue

        stats_row = matching_stats.iloc[0]

        # 数据验证：确保年份匹配
        if stats_row['monitoring_year'] != exec_row['report_year']:
            logger.warning(f'年份不匹配: 执行报告年份={exec_row["report_year"]}, '
                          f'自行监测年份={stats_row["monitoring_year"]}')
            continue

        # 将data_type转换为pollutant_type
        pollutant_type = 'air' if data_type == 'waste_gas' else 'water'

        # 创建基础的合并记录，包含所有异常类型键（初始为空字典）
        merged_record = {
            'pollutant_type': pollutant_type,                         # 污染物类型：'air'(废气) 或 'water'(废水)
            'f_enterprise_id': exec_row['f_enterprise_id'],           # 执行报告中的企业ID
            'f_enterprise_name': exec_row['f_enterprise_name'],       # 执行报告中的企业名称
            'std_code': exec_row['std_code'],                         # 污染物标准编码
            'outlet_id': exec_row['outlet_id'],                       # 排放口编号
            'monitoring_site_data': exec_row['monitoring_site_data'], # 监测点位数据
            'monitoring_indicator_data': exec_row['monitoring_indicator_data'], # 监测指标数据（污染因子）
            'report_year': exec_row['report_year'],                   # 执行报告年份
            'count': {},                                              # 有效监测数据数量不匹配（初始为空）
            'max': {},                                                # 最大值不匹配（初始为空）
            'min': {},                                                # 最小值不匹配（初始为空）
            'avg': {}                                                 # 平均值不匹配（初始为空）
        }

        # 标记是否有异常
        has_anomaly = False

        # 校验有效监测数据数量
        exec_count = exec_row['valid_monitoring_data_count']
        self_count = stats_row['count']

        if pd.notna(exec_count) and exec_count != self_count:
            merged_record['count'] = {
                'exec_value': exec_count,                             # 执行报告中的值（有效监测数据数量）
                'self_value': float(self_count),                      # 自行监测统计的值（有效监测数据数量）
                'difference': float(abs(exec_count - self_count))     # 两值之间的差异（绝对值）
            }
            has_anomaly = True
            # 如果数量不匹配，跳过其他校验
            if has_anomaly:
                anomalies.append(merged_record)
            continue

        # 校验最大值（5%容差）
        exec_max = exec_row[max_col]
        self_max = stats_row['max']

        if pd.notna(exec_max) and pd.notna(self_max):
            if not is_within_tolerance(exec_max, self_max):
                merged_record['max'] = {
                    'exec_value': exec_max,                           # 执行报告中的最大值
                    'self_value': float(self_max),                    # 自行监测统计的最大值
                    'difference': float(abs(exec_max - self_max))     # 两值之间的差异（绝对值）
                }
                has_anomaly = True

        # 校验最小值（特殊规则：执行报告的最小值小于自行监测的非0最小值不算异常）
        exec_min = exec_row[min_col]
        self_min = stats_row['min']
        self_non_zero_min = stats_row['non_zero_min']

        if pd.notna(exec_min) and pd.notna(self_min):
            # 如果执行报告最小值小于自行监测非0最小值，不算异常
            if pd.notna(self_non_zero_min) and exec_min < self_non_zero_min:
                pass  # 不算异常
            elif not is_within_tolerance(exec_min, self_min):
                merged_record['min'] = {
                    'exec_value': exec_min,                           # 执行报告中的最小值
                    'self_value': float(self_min),                    # 自行监测统计的最小值
                    'difference': float(abs(exec_min - self_min))     # 两值之间的差异（绝对值）
                }
                has_anomaly = True

        # 校验平均值（5%容差或差值小于0.1）
        exec_avg = exec_row[avg_col]
        self_avg = stats_row['mean']

        if pd.notna(exec_avg) and pd.notna(self_avg):
            if not (is_within_tolerance(exec_avg, self_avg) or abs(exec_avg - self_avg) < 0.1):
                merged_record['avg'] = {
                    'exec_value': exec_avg,                           # 执行报告中的平均值
                    'self_value': float(self_avg),                    # 自行监测统计的平均值
                    'difference': float(abs(exec_avg - self_avg))     # 两值之间的差异（绝对值）
                }
                has_anomaly = True

        # 只有当至少存在一种异常时，才添加到结果列表
        if has_anomaly:
            anomalies.append(merged_record)

    logger.info(f'{data_type}数据比较完成，发现{len(anomalies)}个异常')

    return anomalies


def is_within_tolerance(value1: float, value2: float, tolerance: float = None) -> bool:
    """
    判断两个值是否在容差范围内

    Args:
        value1 (float): 值1
        value2 (float): 值2
        tolerance (float): 容差比例，默认使用配置值

    Returns:
        bool: 是否在容差范围内
    """
    if tolerance is None:
        tolerance = CONFIG['tolerance']

    if value2 == 0:
        return value1 == 0

    # 使用较大值作为基准计算相对误差
    base_value = max(abs(value1), abs(value2))
    return abs(value1 - value2) / base_value <= tolerance


def validate_config() -> bool:
    """
    验证配置参数的有效性

    Returns:
        bool: 配置是否有效
    """
    issues = []

    # 验证容差配置
    if not (0 <= CONFIG['tolerance'] <= 1):
        issues.append(f"tolerance配置无效: {CONFIG['tolerance']}，应在0-1之间")

    if CONFIG['avg_absolute_tolerance'] < 0:
        issues.append(f"avg_absolute_tolerance配置无效: {CONFIG['avg_absolute_tolerance']}，应大于等于0")

    if CONFIG['cache_timeout'] <= 0:
        issues.append(f"cache_timeout配置无效: {CONFIG['cache_timeout']}，应大于0")

    if CONFIG['batch_size'] <= 0:
        issues.append(f"batch_size配置无效: {CONFIG['batch_size']}，应大于0")

    if issues:
        for issue in issues:
            logger.error(f"配置验证失败: {issue}")
        return False

    logger.debug("配置验证通过")
    return True


def clear_cache():
    """清空缓存"""
    global _CACHE
    _CACHE = {
        'city_codes': {},
        'enterprise_mappings': {},
        'last_update': {}
    }
    logger.info('缓存已清空')


def update_performance_stats(run_time: float, exec_gas_count: int, exec_water_count: int,
                           self_gas_count: int, self_water_count: int, anomalies: List[Dict]):
    """
    更新性能统计信息

    Args:
        run_time: 运行时间（秒）
        exec_gas_count: 执行报告废气记录数
        exec_water_count: 执行报告废水记录数
        self_gas_count: 自行监测废气记录数
        self_water_count: 自行监测废水记录数
        anomalies: 异常记录列表
    """
    global _PERFORMANCE_STATS

    _PERFORMANCE_STATS['total_runs'] += 1
    _PERFORMANCE_STATS['total_time'] += run_time
    _PERFORMANCE_STATS['avg_time'] = _PERFORMANCE_STATS['total_time'] / _PERFORMANCE_STATS['total_runs']
    _PERFORMANCE_STATS['last_run_time'] = run_time

    # 更新数据处理统计
    _PERFORMANCE_STATS['data_processed']['exec_gas_records'] += exec_gas_count
    _PERFORMANCE_STATS['data_processed']['exec_water_records'] += exec_water_count
    _PERFORMANCE_STATS['data_processed']['self_gas_records'] += self_gas_count
    _PERFORMANCE_STATS['data_processed']['self_water_records'] += self_water_count

    # 更新异常统计（基于新的数据结构）
    _PERFORMANCE_STATS['anomalies_found']['total'] += len(anomalies)
    for anomaly in anomalies:
        # 统计每种异常类型的出现次数（检查值是否为非空字典）
        if anomaly.get('count', {}):
            _PERFORMANCE_STATS['anomalies_found']['by_type']['有效监测数据数量不匹配'] += 1
        if anomaly.get('max', {}):
            _PERFORMANCE_STATS['anomalies_found']['by_type']['最大值不匹配'] += 1
        if anomaly.get('min', {}):
            _PERFORMANCE_STATS['anomalies_found']['by_type']['最小值不匹配'] += 1
        if anomaly.get('avg', {}):
            _PERFORMANCE_STATS['anomalies_found']['by_type']['平均值不匹配'] += 1


def get_performance_stats() -> Dict:
    """获取性能统计信息"""
    stats = _PERFORMANCE_STATS.copy()
    stats['anomalies_found']['by_type'] = dict(stats['anomalies_found']['by_type'])
    return stats


def print_performance_summary():
    """打印性能摘要"""
    stats = get_performance_stats()

    logger.info("=== 性能统计摘要 ===")
    logger.info(f"总运行次数: {stats['total_runs']}")
    logger.info(f"总运行时间: {stats['total_time']:.2f} 秒")
    logger.info(f"平均运行时间: {stats['avg_time']:.2f} 秒")
    logger.info(f"最近运行时间: {stats['last_run_time']:.2f} 秒")

    logger.info("数据处理统计:")
    data_stats = stats['data_processed']
    logger.info(f"  执行报告废气: {data_stats['exec_gas_records']} 条")
    logger.info(f"  执行报告废水: {data_stats['exec_water_records']} 条")
    logger.info(f"  自行监测废气: {data_stats['self_gas_records']} 条")
    logger.info(f"  自行监测废水: {data_stats['self_water_records']} 条")

    logger.info(f"异常发现统计:")
    logger.info(f"  总异常数: {stats['anomalies_found']['total']}")
    for anomaly_type, count in stats['anomalies_found']['by_type'].items():
        logger.info(f"  {anomaly_type}: {count} 条")


def get_cache_info() -> Dict:
    """获取缓存信息"""
    return {
        'city_codes_count': len(_CACHE['city_codes']),
        'enterprise_mappings_count': len(_CACHE['enterprise_mappings']),
        'cache_timeout': CONFIG['cache_timeout']
    }


def analyze_data_year_distribution(exec_data: pd.DataFrame, self_data: pd.DataFrame, data_type: str) -> Dict:
    """
    分析数据的年份分布情况

    Args:
        exec_data: 执行报告数据
        self_data: 自行监测数据
        data_type: 数据类型

    Returns:
        Dict: 年份分布统计信息
    """
    analysis = {
        'data_type': data_type,
        'exec_years': [],
        'self_years': [],
        'common_years': [],
        'exec_only_years': [],
        'self_only_years': []
    }

    if not exec_data.empty and 'report_year' in exec_data.columns:
        exec_years = set(exec_data['report_year'].dropna().unique())
        analysis['exec_years'] = sorted(exec_years)

    if not self_data.empty and 'monitoring_year' in self_data.columns:
        self_years = set(self_data['monitoring_year'].dropna().unique())
        analysis['self_years'] = sorted(self_years)

    # 计算交集和差集
    exec_years_set = set(analysis['exec_years'])
    self_years_set = set(analysis['self_years'])

    analysis['common_years'] = sorted(exec_years_set & self_years_set)
    analysis['exec_only_years'] = sorted(exec_years_set - self_years_set)
    analysis['self_only_years'] = sorted(self_years_set - exec_years_set)

    return analysis


def validate_data_quality(exec_data: pd.DataFrame, self_data: pd.DataFrame, data_type: str) -> Dict:
    """
    验证数据质量

    Args:
        exec_data: 执行报告数据
        self_data: 自行监测数据
        data_type: 数据类型

    Returns:
        Dict: 数据质量报告
    """
    quality_report = {
        'data_type': data_type,
        'exec_data_issues': [],
        'self_data_issues': [],
        'mapping_issues': []
    }

    # 检查执行报告数据质量
    if not exec_data.empty:
        # 检查必要字段
        required_exec_fields = ['f_enterprise_id', 'monitoring_site_data', 'monitoring_indicator_data', 'report_year']
        for field in required_exec_fields:
            if field not in exec_data.columns:
                quality_report['exec_data_issues'].append(f'缺少必要字段: {field}')
            elif exec_data[field].isna().any():
                null_count = exec_data[field].isna().sum()
                quality_report['exec_data_issues'].append(f'字段{field}有{null_count}个空值')

        # 检查年份数据
        if 'report_year' in exec_data.columns:
            invalid_years = exec_data[
                (exec_data['report_year'] < 2010) | (exec_data['report_year'] > 2030)
            ]
            if not invalid_years.empty:
                quality_report['exec_data_issues'].append(f'发现{len(invalid_years)}条异常年份数据')

    # 检查自行监测数据质量
    if not self_data.empty:
        # 检查必要字段
        required_self_fields = ['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 'monitoring_year']
        for field in required_self_fields:
            if field not in self_data.columns:
                quality_report['self_data_issues'].append(f'缺少必要字段: {field}')
            elif self_data[field].isna().any():
                null_count = self_data[field].isna().sum()
                quality_report['self_data_issues'].append(f'字段{field}有{null_count}个空值')

        # 检查年份数据
        if 'monitoring_year' in self_data.columns:
            invalid_years = self_data[
                (self_data['monitoring_year'] < 2010) | (self_data['monitoring_year'] > 2030)
            ]
            if not invalid_years.empty:
                quality_report['self_data_issues'].append(f'发现{len(invalid_years)}条异常年份数据')

    # 检查映射问题
    if not exec_data.empty and not self_data.empty:
        if 'self_id' in exec_data.columns and 'self_id' in self_data.columns:
            exec_enterprises = set(exec_data['self_id'].dropna())
            self_enterprises = set(self_data['self_id'].dropna())

            unmapped_exec = exec_enterprises - self_enterprises
            unmapped_self = self_enterprises - exec_enterprises

            if unmapped_exec:
                quality_report['mapping_issues'].append(f'执行报告中有{len(unmapped_exec)}个企业在自行监测中无对应数据')
            if unmapped_self:
                quality_report['mapping_issues'].append(f'自行监测中有{len(unmapped_self)}个企业在执行报告中无对应数据')

    return quality_report


def check_waste_gas_data(city: str, years: Optional[List[int]] = None) -> List[Dict]:
    """
    废气数据校验主函数

    Args:
        city (str): 城市名称
        years (List[int], optional): 年份列表，如[2024, 2025]

    Returns:
        list: 废气异常记录列表
    """
    logger.info(f'开始校验{city}的废气数据...')
    if years:
        logger.info(f'年份筛选: {years}')

    try:
        # 1. 获取城市编码
        city_code = get_city_code(city)

        # 2. 获取企业关联映射
        enterprise_mapping = get_enterprise_mapping(city_code)

        if enterprise_mapping.empty:
            logger.warning(f'{city}没有企业关联映射数据')
            return []

        # 3. 提取执行报告数据（支持年份筛选）
        exec_gas_data = extract_execution_report_gas_data(city_code, years)

        if exec_gas_data.empty:
            logger.warning(f'{city}没有废气执行报告数据')
            return []

        # 4. 提取自行监测数据（支持年份筛选）
        self_gas_data = extract_self_monitoring_gas_data(city_code, years)

        if self_gas_data.empty:
            logger.warning(f'{city}没有废气自行监测数据')
            return []

        # 5. 关联企业映射
        self_gas_data = pd.merge(
            self_gas_data,
            enterprise_mapping[['self_id', 'f_enterprise_id']],
            on='self_id',
            how='inner'
        )

        exec_gas_data = pd.merge(
            exec_gas_data,
            enterprise_mapping[['f_enterprise_id', 'self_id']],
            on='f_enterprise_id',
            how='inner'
        )

        # 6. 计算自行监测统计数据（按年份分组）
        group_keys = ['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 'monitoring_year']
        self_gas_stats = calculate_monitoring_stats(self_gas_data, group_keys)

        # 7. 比较数据
        gas_anomalies = compare_monitoring_data(exec_gas_data, self_gas_stats, 'waste_gas')

        logger.info(f'废气数据校验完成，发现{len(gas_anomalies)}个异常')

        return gas_anomalies

    except Exception as e:
        logger.error(f'废气数据校验过程中发生错误: {str(e)}')
        raise


def check_waste_water_data(city: str, years: Optional[List[int]] = None) -> List[Dict]:
    """
    废水数据校验主函数

    Args:
        city (str): 城市名称
        years (List[int], optional): 年份列表，如[2024, 2025]

    Returns:
        list: 废水异常记录列表
    """
    logger.info(f'开始校验{city}的废水数据...')
    if years:
        logger.info(f'年份筛选: {years}')

    try:
        # 1. 获取城市编码
        city_code = get_city_code(city)

        # 2. 获取企业关联映射
        enterprise_mapping = get_enterprise_mapping(city_code)

        if enterprise_mapping.empty:
            logger.warning(f'{city}没有企业关联映射数据')
            return []

        # 3. 提取执行报告数据（支持年份筛选）
        exec_water_data = extract_execution_report_water_data(city_code, years)

        if exec_water_data.empty:
            logger.warning(f'{city}没有废水执行报告数据')
            return []

        # 4. 提取自行监测数据（支持年份筛选）
        # 注意：废水数据会自动计算日平均值
        self_water_data = extract_self_monitoring_water_data(city_code, years)

        if self_water_data.empty:
            logger.warning(f'{city}没有废水自行监测数据')
            return []

        # 5. 关联企业映射
        self_water_data = pd.merge(
            self_water_data,
            enterprise_mapping[['self_id', 'f_enterprise_id']],
            on='self_id',
            how='inner'
        )

        exec_water_data = pd.merge(
            exec_water_data,
            enterprise_mapping[['f_enterprise_id', 'self_id']],
            on='f_enterprise_id',
            how='inner'
        )

        # 6. 计算自行监测统计数据（按年份分组）
        group_keys = ['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 'monitoring_year']
        self_water_stats = calculate_monitoring_stats(self_water_data, group_keys)

        # 7. 比较数据
        water_anomalies = compare_monitoring_data(exec_water_data, self_water_stats, 'waste_water')

        logger.info(f'废水数据校验完成，发现{len(water_anomalies)}个异常')

        return water_anomalies

    except Exception as e:
        logger.error(f'废水数据校验过程中发生错误: {str(e)}')
        raise


@timing_decorator
def check_exe_with_self_monitor(city: str, years: Optional[List[int]] = None) -> List[Dict]:
    """
    执行报告与自行监测数据校验主入口函数（支持年份筛选）

    Args:
        city (str): 城市名称
        years (List[int], optional): 年份列表，如[2024, 2025]

    Returns:
        list: 所有异常记录列表
    """
    logger.info(f'开始执行报告与自行监测数据校验 - 城市: {city}')
    if years:
        logger.info(f'年份筛选: {years}')

    # 验证配置
    if not validate_config():
        raise ValueError("配置验证失败，请检查CONFIG参数设置")

    try:
        # 一次性获取所有基础数据
        city_code = get_city_code(city)
        enterprise_mapping = get_enterprise_mapping(city_code)

        if enterprise_mapping.empty:
            logger.warning(f'{city}没有企业关联映射数据')
            return []

        # 一次性提取所有执行报告和自行监测数据（支持年份筛选）
        exec_gas_data, exec_water_data = extract_all_execution_report_data(city_code, years)
        self_gas_data, self_water_data_raw = extract_all_self_monitoring_data(city_code, years)

        # 对废水自行监测数据计算日平均值
        if not self_water_data_raw.empty:
            self_water_data = calculate_daily_average_for_water(self_water_data_raw)
        else:
            self_water_data = self_water_data_raw

        # 分析数据年份分布
        if not exec_gas_data.empty or not self_gas_data.empty:
            gas_analysis = analyze_data_year_distribution(exec_gas_data, self_gas_data, 'waste_gas')
            logger.info(f'废气数据年份分布: 执行报告{gas_analysis["exec_years"]}, '
                       f'自行监测{gas_analysis["self_years"]}, '
                       f'共同年份{gas_analysis["common_years"]}')

        if not exec_water_data.empty or not self_water_data.empty:
            water_analysis = analyze_data_year_distribution(exec_water_data, self_water_data, 'waste_water')
            logger.info(f'废水数据年份分布: 执行报告{water_analysis["exec_years"]}, '
                       f'自行监测{water_analysis["self_years"]}, '
                       f'共同年份{water_analysis["common_years"]}')

        # 数据质量验证
        if not exec_gas_data.empty or not self_gas_data.empty:
            gas_quality = validate_data_quality(exec_gas_data, self_gas_data, 'waste_gas')
            if gas_quality['exec_data_issues'] or gas_quality['self_data_issues'] or gas_quality['mapping_issues']:
                logger.warning(f'废气数据质量问题: {gas_quality}')
            else:
                logger.info('废气数据质量检查通过')

        if not exec_water_data.empty or not self_water_data.empty:
            water_quality = validate_data_quality(exec_water_data, self_water_data, 'waste_water')
            if water_quality['exec_data_issues'] or water_quality['self_data_issues'] or water_quality['mapping_issues']:
                logger.warning(f'废水数据质量问题: {water_quality}')
            else:
                logger.info('废水数据质量检查通过')

        # 并行处理废气和废水数据
        gas_anomalies = []
        water_anomalies = []

        if not exec_gas_data.empty and not self_gas_data.empty:
            gas_anomalies = process_data_comparison(
                exec_gas_data, self_gas_data, enterprise_mapping, 'waste_gas'
            )

        if not exec_water_data.empty and not self_water_data.empty:
            water_anomalies = process_data_comparison(
                exec_water_data, self_water_data, enterprise_mapping, 'waste_water'
            )

        # 合并结果
        all_anomalies = gas_anomalies + water_anomalies

        logger.info(f'执行报告与自行监测数据校验完成 - 总异常记录: {len(all_anomalies)} 条 '
                    f'(废气: {len(gas_anomalies)} 条, 废水: {len(water_anomalies)} 条)')

        return all_anomalies

    except Exception as e:
        logger.error(f'执行报告与自行监测数据校验过程中发生错误: {str(e)}')
        raise


def process_data_comparison(exec_data: pd.DataFrame, self_data: pd.DataFrame,
                          enterprise_mapping: pd.DataFrame, data_type: str) -> List[Dict]:
    """
    处理数据比较的通用函数

    Args:
        exec_data: 执行报告数据
        self_data: 自行监测数据
        enterprise_mapping: 企业关联映射
        data_type: 数据类型

    Returns:
        List[Dict]: 异常记录列表
    """
    # 关联企业映射
    self_data = pd.merge(
        self_data,
        enterprise_mapping[['self_id', 'f_enterprise_id']],
        on='self_id',
        how='inner'
    )

    exec_data = pd.merge(
        exec_data,
        enterprise_mapping[['f_enterprise_id', 'self_id']],
        on='f_enterprise_id',
        how='inner'
    )

    # 计算自行监测统计数据（按年份分组）
    group_keys = ['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 'monitoring_year']
    self_stats = calculate_monitoring_stats(self_data, group_keys)

    # 比较数据
    anomalies = compare_monitoring_data(exec_data, self_stats, data_type)

    return anomalies


def analyze_anomalies(anomalies: List[Dict]) -> Dict:
    """
    分析异常记录统计信息

    Args:
        anomalies: 异常记录列表

    Returns:
        Dict: 统计分析结果
    """
    if not anomalies:
        return {
            'total_count': 0,
            'by_pollutant_type': {},
            'by_anomaly_type': {},
            'by_enterprise': {},
            'by_pollutant': {},
            'top_enterprises': [],
            'top_pollutants': []
        }

    analysis = {
        'total_count': len(anomalies),
        'by_pollutant_type': {},
        'by_anomaly_type': {},
        'by_enterprise': {},
        'by_pollutant': {},
        'top_enterprises': [],
        'top_pollutants': []
    }

    # 按污染物类型统计
    for anomaly in anomalies:
        pollutant_type = anomaly.get('pollutant_type', 'unknown')
        analysis['by_pollutant_type'][pollutant_type] = analysis['by_pollutant_type'].get(pollutant_type, 0) + 1

    # 按异常类型统计（基于新的数据结构）
    for anomaly in anomalies:
        # 统计每种异常类型的出现次数（检查值是否为非空字典）
        if anomaly.get('count', {}):
            analysis['by_anomaly_type']['有效监测数据数量不匹配'] = analysis['by_anomaly_type'].get('有效监测数据数量不匹配', 0) + 1
        if anomaly.get('max', {}):
            analysis['by_anomaly_type']['最大值不匹配'] = analysis['by_anomaly_type'].get('最大值不匹配', 0) + 1
        if anomaly.get('min', {}):
            analysis['by_anomaly_type']['最小值不匹配'] = analysis['by_anomaly_type'].get('最小值不匹配', 0) + 1
        if anomaly.get('avg', {}):
            analysis['by_anomaly_type']['平均值不匹配'] = analysis['by_anomaly_type'].get('平均值不匹配', 0) + 1

    # 按企业统计
    for anomaly in anomalies:
        enterprise = anomaly.get('f_enterprise_name', 'unknown')
        analysis['by_enterprise'][enterprise] = analysis['by_enterprise'].get(enterprise, 0) + 1

    # 按污染因子统计
    for anomaly in anomalies:
        pollutant = anomaly.get('monitoring_indicator_data', 'unknown')
        analysis['by_pollutant'][pollutant] = analysis['by_pollutant'].get(pollutant, 0) + 1

    # 获取前5名问题企业
    analysis['top_enterprises'] = sorted(
        analysis['by_enterprise'].items(),
        key=lambda x: x[1],
        reverse=True
    )[:5]

    # 获取前5名问题污染因子
    analysis['top_pollutants'] = sorted(
        analysis['by_pollutant'].items(),
        key=lambda x: x[1],
        reverse=True
    )[:5]

    return analysis


def print_analysis_summary(analysis: Dict):
    """
    打印分析摘要

    Args:
        analysis: 分析结果
    """
    logger.info("=== 异常记录分析摘要 ===")
    logger.info(f"异常记录总数: {analysis['total_count']}")

    if analysis['total_count'] > 0:
        # 按污染物类型统计
        logger.info("按污染物类型分布:")
        for pollutant_type, count in analysis['by_pollutant_type'].items():
            type_name = '废气' if pollutant_type == 'air' else '废水' if pollutant_type == 'water' else pollutant_type
            logger.info(f"  {type_name}: {count} 条")

        # 按异常类型统计
        logger.info("按异常类型分布:")
        for anomaly_type, count in analysis['by_anomaly_type'].items():
            logger.info(f"  {anomaly_type}: {count} 条")

        # 前5名问题企业
        if analysis['top_enterprises']:
            logger.info("问题企业TOP5:")
            for i, (enterprise, count) in enumerate(analysis['top_enterprises'], 1):
                logger.info(f"  {i}. {enterprise}: {count} 条")

        # 前5名问题污染因子
        if analysis['top_pollutants']:
            logger.info("问题污染因子TOP5:")
            for i, (pollutant, count) in enumerate(analysis['top_pollutants'], 1):
                logger.info(f"  {i}. {pollutant}: {count} 条")


def run(city: str, years: Optional[List[int]] = None, show_analysis: bool = True,
        export_format: str = None, show_performance: bool = False) -> List[Dict]:
    """
    主函数：执行报告与自行监测数据校验

    Args:
        city (str): 城市名称
        years (List[int], optional): 年份列表，如[2024, 2025]
        show_analysis (bool): 是否显示分析摘要，默认True
        export_format (str): 导出格式，'csv'或'json'，默认不导出
        show_performance (bool): 是否显示性能统计，默认False

    Returns:
        list: 异常记录列表
    """
    start_time = time.time()

    # 执行校验
    anomalies = check_exe_with_self_monitor(city, years)

    end_time = time.time()
    run_time = end_time - start_time

    # 更新性能统计（这里需要从实际数据中获取记录数，暂时使用0）
    update_performance_stats(run_time, 0, 0, 0, 0, anomalies)

    # 显示分析摘要
    if show_analysis:
        analysis = analyze_anomalies(anomalies)
        print_analysis_summary(analysis)

    # 显示性能统计
    if show_performance:
        print_performance_summary()

    return anomalies


if __name__ == '__main__':
    # 示例调用
    city = '九龙坡区'

    # # 不指定年份（查询所有数据）
    # print("=== 查询所有年份数据 ===")
    # result_all = run(city, show_analysis=True)
    # print(f"发现异常记录: {len(result_all)} 条")

    # 指定年份筛选
    print("\n=== 查询2023年数据 ===")
    result_years = run(city, years=[2023], show_analysis=True)
    print(f"发现异常记录: {len(result_years)} 条")
    print(result_years[:5])

    # 显示前几条异常记录的详细信息
    if result_years:
        print("\n=== 异常记录示例 ===")
        for i, anomaly in enumerate(result_years[:2]):  # 只显示前2条
            print(f"\n异常记录 {i+1}:")
            print(f"  污染物类型: {anomaly.get('pollutant_type', 'N/A')}")
            print(f"  企业名称: {anomaly.get('f_enterprise_name', 'N/A')}")
            print(f"  污染因子: {anomaly.get('monitoring_indicator_data', 'N/A')}")
            print(f"  异常类型: {anomaly.get('anomaly_type', 'N/A')}")
            print(f"  执行报告值: {anomaly.get('exec_value', 'N/A')}")
            print(f"  自行监测值: {anomaly.get('self_value', 'N/A')}")
            print(f"  差异值: {anomaly.get('difference', 'N/A')}")

    # 测试配置验证
    print("\n=== 配置验证测试 ===")
    if validate_config():
        print("✅ 当前配置有效")
    else:
        print("❌ 当前配置无效")

    # 显示缓存信息
    print("\n=== 缓存信息 ===")
    cache_info = get_cache_info()
    print(f"城市编码缓存: {cache_info['city_codes_count']} 条")
    print(f"企业映射缓存: {cache_info['enterprise_mappings_count']} 条")
    print(f"缓存超时时间: {cache_info['cache_timeout']} 秒")
