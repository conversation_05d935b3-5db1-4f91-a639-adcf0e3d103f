# -*- coding: utf-8 -*-
"""
@File  : check_commit_time.py.py
@Author: <PERSON>
@Date  : 2025/1/13 11:20
@Desc  : 执行报告频次校验
"""
import os
from collections import defaultdict
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from lib import ori_database_conf, proj_dir, ALL_ENTS, Global
from lib.check.base import print_format, clean_null, get_city_enterprises
from mod import DB, save_yaml


def calc_absences(start_time, now_time, data):
    """
    计算从start_time到now_time之间，各个实体缺少的报告期数。

    参数:
    start_time (str): 开始时间，格式为'YYYY-MM-DD'。
    now_time (str): 当前时间，格式为'YYYY-MM-DD'。
    base_path (str): 存放报告的基础路径。

    返回:
    defaultdict: 包含每个实体缺少的年报、季报、月报的字典。
    """
    # 将字符串时间转换为datetime对象
    start_time = datetime.strptime(start_time, '%Y-%m-%d')
    now_time = datetime.strptime(now_time, '%Y-%m-%d')
    # 减去20天是为了确保计算时考虑到可能的报告延迟
    now_time = now_time - timedelta(days=20)

    # 计算需要考虑的年份范围
    need_years = list(range(start_time.year, now_time.year))

    # 初始化需要的季度列表，并计算从开始时间到当前时间的所有季度
    need_quarters = []
    tmp = now_time - relativedelta(months=3)
    while tmp >= start_time:
        need_quarters.append([tmp.year, (tmp.month-1)//3+1])
        tmp = tmp - relativedelta(months=3)

    # 初始化需要的月份列表，并计算从开始时间到当前时间的所有月份
    need_months = []
    tmp = now_time - relativedelta(months=1)
    while tmp >= start_time:
        need_months.append([tmp.year, tmp.month])
        tmp = tmp - relativedelta(months=1)

    # 初始化缺交报告的字典
    absences = defaultdict(dict)
    # 遍历每个实体和其要求的报告类型
    for ent, required in FREQ_REQUIRED.items():
        # 获取实体对应的文件列表
        files = ','.join(data[ent]) if ent in data else ''
        # 如果没有文件，根据要求的报告类型，将所有年份、季度、月份视为缺失
        if not files:
            if '年报' in required:
                absences[ent]['year'] = need_years
            if '季报' in required:
                absences[ent]['quarter'] = need_quarters
            if '月报' in required:
                absences[ent]['month'] = need_months
            continue

        # 检查月报的缺失情况
        if '月报' in required:
            for year, month in need_months:
                if f'{year}年{month:02d}月' not in files and f'{year}年{month}月' not in files:
                    if 'month' not in absences[ent]:
                        absences[ent]['month'] = [[year, month]]
                    else:
                        absences[ent]['month'].append([year, month])
        # 检查季报的缺失情况
        if '季报' in required:
            for year, quarter in need_quarters:
                if f'{year}年第{quarter:02d}季' not in files and f'{year}年第{quarter}季' not in files:
                    if 'quarter' not in absences[ent]:
                        absences[ent]['quarter'] = [[year, quarter]]
                    else:
                        absences[ent]['quarter'].append([year, quarter])
                else:
                    last_month = 3 * quarter
                    if (ent in absences
                        and 'month' in absences[ent]
                        and [year, last_month] in absences[ent]['month']
                    ):
                        absences[ent]['month'].remove([year, last_month])
        # 检查年报的缺失情况
        if '年报' in required:
            for year in need_years:
                if f'{year}年年报表' not in files:
                    if 'year' not in absences[ent]:
                        absences[ent]['year'] = [year]
                    else:
                        absences[ent]['year'].append(year)
                else:
                    if (ent in absences
                        and 'quarter' in absences[ent]
                        and [year, 4] in absences[ent]['quarter']
                    ):
                        absences[ent]['quarter'].remove([year, 4])

    return absences


def get_data(city):
    if city in Global.GET_DATA:
        return Global.GET_DATA[city]
    host = ori_database_conf['host']
    port = ori_database_conf['port']
    dbname = ori_database_conf['database']
    user = ori_database_conf['user']
    password = ori_database_conf['password']
    # 获取企业列表
    enterprises = get_city_enterprises(city)

    db = DB(host, port, dbname, user, password)
    ents = ', '.join([f"'{ent}'" for ent in enterprises])

    sql = """
        select f_enterprise_id, f_enterprise_name, f_report_frequency
        from t_pw_enterprise_zx_yq_report t
        where t.f_enterprise_name in ({ents})
    """
    standard = db.query_sql(sql.format(ents=ents))

    if not standard.empty:
        standard['enterprise'] = standard['f_enterprise_id'].astype(str) + '(' + standard['f_enterprise_name'] + ')'
        standard = standard[['enterprise', 'f_report_frequency']].copy()
        standard = standard.groupby('enterprise')['f_report_frequency'].apply(list).reset_index()
        standard = standard.set_index('enterprise')['f_report_frequency'].to_dict()
        save_yaml(standard, os.path.join(proj_dir, 'config', 'freq_required.yml'))

    sql = """
        select f_enterprise_id, f_enterprise_name, f_report_time
        from t_pw_enterprise_zx_report t 
        where t.f_enterprise_name in ({ents})
    """
    standard_exe = db.query_sql(sql.format(ents=ents))

    if not standard_exe.empty:
        standard_exe['enterprise'] = standard_exe['f_enterprise_id'].astype(str) + '(' + standard_exe['f_enterprise_name'] + ')'
        standard_exe = standard_exe[['enterprise', 'f_report_time']].copy()
        standard_exe = standard_exe.groupby('enterprise')['f_report_time'].apply(list).reset_index()
        standard_exe = standard_exe.set_index('enterprise')['f_report_time'].to_dict()

    db.close()
    Global.GET_DATA[city] = (standard, standard_exe)
    return Global.GET_DATA[city]


def run(city, start, now):
    standard, standard_exe = get_data(city)
    global FREQ_REQUIRED
    FREQ_REQUIRED = standard
    now = datetime.strptime(now, '%Y-%m-%d')
    now = now - relativedelta(months=1)
    # 转回字符串
    now = now.strftime('%Y-%m-%d')
    absences = calc_absences(start, now, standard_exe)
    clean_null(absences)
    return absences


def run_warn(city, start_time, end_time):
    standard, standard_exe = get_data(city)
    global FREQ_REQUIRED
    FREQ_REQUIRED = standard
    absences = calc_absences(start_time, end_time, standard_exe)

    end_time = datetime.strptime(end_time, '%Y-%m-%d')
    # 减一个月的时间
    end_time = end_time - relativedelta(months=1)
    # 所在的年、季、月
    remain_month = end_time.month
    remain_quarter = (remain_month - 1) // 3 + 1
    remain_year = end_time.year

    for ent, all_type in absences.items():
        for _type, group in all_type.items():
            if _type == 'month':
                if [remain_year, remain_month] in group:
                    all_type[_type] = [[remain_year, remain_month]]
                else:
                    all_type[_type] = []
            elif _type == 'quarter':
                if [remain_year, remain_quarter] in group:
                    all_type[_type] = [[remain_year, remain_quarter]]
                else:
                    all_type[_type] = []
            else:
                if remain_year in group:
                    all_type[_type] = [remain_year]
                else:
                    all_type[_type] = []
    clean_null(absences)
    return absences


if __name__ == '__main__':
    start_time = '2021-03-01'
    now_time = '2023-02-21'
    absences = run(start_time, now_time)
    print_format(absences)
    print('len(absences)', len(absences))
