# -*- coding: utf-8 -*-
"""
@File  : check_limit_val.py
@Author: <PERSON>
@Date  : 2025/1/22 11:02
@Desc  : 
"""
import pandas as pd
import os
import pickle
from openpyxl.utils.dataframe import dataframe_to_rows
from wcwidth import wcswidth as _wcswidth
import re
import json

from lib import proj_dir
from lib.check.base import extract_tbl_from_tbl, listdir
from lib.llm.step_3_1_extract_info_from_file import request_model_qwen2_72b

pd.set_option('display.width', 300, 'display.max_columns', 10, 'display.max_rows', 50)
llm_kwargs = {'temperature': 0.5}


def wcswidth(pwcs, n=None, unicode_version='auto'):
    pwcs = pwcs.replace('\n', '\\n')
    length = _wcswidth(pwcs, n, unicode_version)
    cnt = pwcs.count('\\n')
    return length - cnt


def extract_limit_val_tbl(file_path):
    """ 提取所有包含排放限值的表 """
    tbls = extract_tbl_from_tbl(file_path)
    limit_val_tbls = {}
    for name, tbl in tbls.items():
        if not name or '排放' not in name:
            continue

        tbl = tbl.fillna('')
        content = '\n'.join(
            ','.join(item).replace('\n', '')
            for _, item in tbl.iterrows()
        )
        if (
            ('排放' in content or '编号' in content)
            and ('污染物' in content or '检测' in content)
            and ('限值' in content or '排放标准' in content)
        ):
            limit_val_tbls[name] = tbl
    return limit_val_tbls


def extract_ents_limit_val_tbls(path):
    """ 提取所有企业环评报告中的排放限值表 """
    ents = sorted([item for item in listdir(path) if os.path.isdir(os.path.join(path, item))])
    dct = {}
    for ent in ents:
        ent_path = os.path.join(path, ent)
        file_names = listdir(ent_path)
        for file_name in file_names:
            if file_name.endswith('.docx'):
                file_path = os.path.join(ent_path, file_name)
                limit_val = extract_limit_val_tbl(file_path)
                if limit_val:
                    dct[ent] = limit_val
                else:
                    print(f'文件 “{ent} / {file_name}” 中没有找到疑似排放限值表')
    return dct


def save_tbls(data, path):
    """ 将数据存储到excel中 """
    with pd.ExcelWriter(path, engine="openpyxl") as writer:
        for sheet_name, dataframes in data.items():
            # 创建一个新的 sheet
            workbook = writer.book
            worksheet = workbook.create_sheet(title=sheet_name)
            writer.sheets[sheet_name] = worksheet

            # 按顺序将 DataFrame 写入该 sheet
            for idx, (title, df) in enumerate(dataframes.items()):
                if title:  # 如果有标题，先写标题
                    worksheet.append([title])
                # 将 DataFrame 转换为行并写入
                for row in dataframe_to_rows(df, index=False, header=True if idx == 0 else False):
                    worksheet.append(row)
                # 添加空行作为分隔
                worksheet.append([])


def rm_line_feed(tbl):
    """ 移除dataframe中的换行符 """
    tbl = tbl.map(lambda x: x.replace('\n', '') if isinstance(x, str) else x)
    return tbl


def save_txts(data, path):
    """ 将所有企业的数据表格式化存储到文本文件中
        适配notepad++与shell显示
    """

    def cut_txt(txt, length):
        ori_len = wcswidth(txt)
        if ori_len > length:
            # 保留省略号之前的内容
            prefix = ''
            i = 1
            while wcswidth(txt[:i]) < ((length-3) // 2):
                prefix = txt[:i]
                i += 1
            omit_txt = prefix + '...'

            # 保留省略号之后的内容
            rest_len = length - wcswidth(omit_txt)
            suffix = ''
            i = 1
            while wcswidth(txt[-i:]) <= rest_len:
                suffix = txt[-i:]
                i += 1
            suffix =  '.' * int(rest_len-wcswidth(suffix)) + suffix
            txt = omit_txt + suffix
        return txt

    def cut_title(txt, length, symbol=' '):
        if wcswidth(txt) > length:
            txt = cut_txt(txt, length)
        rest_len = length - wcswidth(txt)
        if rest_len > 0:
            _txt = symbol * (rest_len // 2) + txt
            txt = _txt + symbol * (length - wcswidth(_txt))
        return txt

    def fill_black(txt, length):
        return ' ' * int(length - wcswidth(txt)) + txt

    fp = open(path, 'w', encoding='utf-8')
    for ent, tbls in data.items():
        # 打印企业名
        sep = '='
        ent = cut_title(ent, 200, sep)
        fp.write('\n\n' + sep * 200 + '\n')
        fp.write(ent + '\n')
        fp.write(sep * 200 + '\n\n')

        for tbl_name, tbl in tbls.items():
            # 打印表名
            tbl_name = cut_title(tbl_name, 200, sep)
            fp.write('\n' + tbl_name + '\n\n')

            tbl = tbl.copy()
            tbl = rm_line_feed(tbl)
            tbl_len = tbl.copy()
            tbl_len = tbl_len.map(lambda x: max(wcswidth(str(x)), 10))
            tbl_len = tbl_len.max().astype(int)
            tbl_len[tbl_len > 30] = 30
            for col, val in tbl_len.items():
                tbl[col] = tbl[col].apply(lambda x: cut_txt(str(x), val))
            delimeter = ' | '
            line = ['+'] +['-' * (length+2) + '+' for length in tbl_len.values]
            line = ''.join(line) + '\n'
            fp.write(line)
            for _, row in tbl.iterrows():
                row = [fill_black(cell, length) for cell, length in zip(row.values, tbl_len.values)]
                fp.write('| ' + delimeter.join(row) + ' |' + '\n')
                fp.write(line)

    return


def llm_extract_limit_val(data, ent):
    """ 大模型从污染物限值相关表中提取限值信息 """

    def embellish(text):
        """
        使用正则表达式移除字符串每一行前面的 4 个空格
        """
        return re.sub(r'^ {4}', '', text, flags=re.MULTILINE)

    prompt = """
    下面给出了{cnt}个“{ent}”有关污染排放的csv格式(英文逗号分割)表格数据，这些表格可能包括但不限于*行业的污染排放标准*，*企业排口基本情况*，*企业排放限值标准*等。
    涉及“废水”或者“废气”的排放。原始表格存在多级索引以及单元格合并的情况，因此转为csv格式后可能有部分相邻单元格有内容的重复，
    你需要仔细分析辨别原始的多级索引，然后深入分析表格内容，最后提取出排放口污染物排放浓度限值。提取要求如下：
    1. 必须同时要有明确的排放口、具体的污染物、以及该企业明确的污染物排放限值要求，行业污染物排放标准只是供你参考；
    2. 你需要判断出排放口是“废水”还是“废气”排口，并在回答中给出分类；
    3. 如果有排放口字母数字编号，最好在回答中写上编号，例如DW001、P1等；
    4. 污染物浓度限值如果有单位，请在回答中给出浓度单位；
    5. 给出的多个表格中可能有内容的部分重叠，可能同一个排口或者同一个污染物在不同的表格中有不同的表述形式，请你仔细分辨，每个排口的污染物只需要写一次；
    6. 请用json格式回答排口污染物的浓度限值要求，最终答案中的json请以```json开始，以```结束，分析过程中的json不用满足开始和结束的要求，最终答案json格式参考如下示例；
    
    ## 回答示例
    ```json
    {{
        "废水":[{{
                "排放口": "DW001",
                "污染物": "COD",
                "限值": "250",
                "单位": "mg/L"
            }},{{
                "排放口": "DW001",
                "污染物": "BOD",
                "限值": "150",
                "单位": "mg/L"
        }}],
        "废气":[...]
    }}
    ```
    
    ## 待分析表格
    
    {tables}
    """
    string = []
    for tbl_name, tbl in data.items():
        tbl = rm_line_feed(tbl)
        csv = tbl.to_csv(index=False, header=False)
        string.append(f'{tbl_name}\n\n{csv}')
    string = '\n\n'.join(string)
    prompt = prompt.format(ent=ent, cnt=len(data), tables=string)
    prompt = embellish(prompt).strip('\n')

    system_prompt = "你是一个专业的环境保护单位执法人员，能够凭借专业知识和经验对违法行为做出准确的判断。"
    user_prompt = [(prompt, '')]

    # 调用模型，获取响应
    ans = request_model_qwen2_72b(system_prompt, user_prompt, use_beam_search=False, **llm_kwargs)

    pattern = r"```json(.*?)```"
    match = re.findall(pattern, ans, re.DOTALL)[-1]
    try:
        ans = json.loads(match)
    except:
        ans = {}
    return ans


if __name__ == '__main__':
    base_path = os.path.join(proj_dir, '材料/2022年环评项目电子档案资料')
    # res = extract_ents_limit_val_tbls(base_path)
    # pickle.dump(res, open('limit_val.pkl', 'wb'))
    res = pickle.load(open('limit_val.pkl', 'rb'))
    # save_txts(res, 'tbls.txt')
    # for k, v in res.items():
    #     print(k)
    #     for i,j in v.items():
    #         print(i)
    #         print(j)
    #         print('='*30)
    for k, v in res.items():
        limit_val = llm_extract_limit_val(v, k)
        break