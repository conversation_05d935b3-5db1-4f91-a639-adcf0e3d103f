# -*- coding: utf-8 -*-
"""
@File  : check_null.py.py
@Author: <PERSON>
@Date  : 2025/1/3 10:07
@Desc  : 无可上报内容应填写“无”或“/”，不允许为空
"""
import os
import pandas as pd
import numpy as np
from lib import TABLE_RULES, TBL_NAME_MAP
from lib.check.base import to_multi_idx, match_table_names
from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx


def val_idx(data, val=''):
    """
    定位所有空值索引

    此函数用于在给定的数据中找到所有与指定值（默认为空字符串）相同的元素的位置
    它返回一个包含这些元素索引的列表，每个索引表示为（行索引，列索引）的元组

    参数:
    data: pandas DataFrame，包含要检查的数据
    val: 要在数据中查找的值，默认为空字符串

    返回:
    res: 包含与指定值相同的元素索引的列表，每个索引为（行索引，列索引）的元组
    """
    # 创建一个布尔 DataFrame，表示 data 中的每个元素是否等于 val
    loc = data.isin([val])
    # 使用 numpy 的 where 方法找到所有 True 值的行索引和列索引
    row, col = np.where(loc)
    # 初始化结果列表，用于存储索引元组
    res = []
    # 遍历行索引和列索引，将它们组合成元组，并添加到结果列表中
    for r, c in zip(row, col):
        res.append((data.index[r], data.columns[c]))
    # 返回结果列表
    return res


def check_null(data, row_num, col_num):
    """
    检查并处理数据中的空值。

    该函数首先将数据转换为具有多级索引的数据框，然后在其中查找并标记空值。

    参数:
    data: DataFrame, 待处理的数据。
    row_num: int, 数据的行数。
    col_num: int, 数据的列数。

    返回:
    DataFrame, 处理后的数据框，其中空值被标记。
    """
    # 将数据转换为多级索引格式
    data = to_multi_idx(data, row_num, col_num)
    # 在转换后的数据中查找并标记空值
    data = val_idx(data, val='')
    # 返回处理后的数据
    return data


def check_doc_null(tables, _type):
    """
    年/季/月报空值定位

    该函数用于检查指定类型报表中的空值，并返回空值的位置信息。

    参数:
    tables (dict): 包含报表数据的字典，键为报表名称，值为报表数据的DataFrame。
    _type (str): 报表类型，用于匹配相应的报表名称和规则。

    返回:
    dict: 包含空值位置信息的字典，键为报表名称，值为空值在报表中的位置信息。
    """
    # 匹配报表名称和对应的规则
    tables, rules = match_table_names(tables, _type)

    # 初始化空值字典，用于存储空值的位置信息
    nan_dct = {}

    # 遍历每个报表及其数据
    for tbl, data in tables.items():
        # 检查并移除备注列
        if '备注' in data.iloc[0, -1]:
            data.drop(data.columns[-1], inplace=True, axis=1)

        # 检查当前报表是否有对应的规则
        if tbl in rules:
            # 获取当前报表的规则
            _rules = rules[tbl]

            # 检查空值并获取空值位置
            nan_loc = check_null(data, row_num=_rules['row_num'], col_num=_rules['col_num'])

            # 如果有空值，则将位置信息存储到字典中
            if nan_loc:
                nan_dct[tbl] = nan_loc
        else:
            # 如果没有对应的规则，抛出运行时错误
            raise RuntimeError(tbl)

    # 返回空值位置信息的字典
    return nan_dct


if __name__ == "__main__":
    base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年重点企业执行报告'
    path = os.path.join(base_path, '重庆市小可食品有限责任公司/执行报告-重庆市小可食品有限责任公司-2022年.docx')
    # path = os.path.join(base_path, '重庆天泰观复新材料有限公司/执行报告-重庆天泰观复新材料有限公司-2022年第02季.docx')
    # path = os.path.join(base_path, '重庆和友实业股份有限公司/执行报告-重庆和友实业股份有限公司-2022年05月.docx')
    docx = MarkdownConverterWithPythonDocx(path)
    tables = docx.tables(format_='dataframe')
    res = check_doc_null(tables, 'year')
    for k, v in res.items():
        print(k)
        for i in v:
            print(i)
        print('-' * 20)