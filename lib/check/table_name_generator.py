# -*- coding: utf-8 -*-
"""
@File  : table_name_generator.py
@Author: <PERSON>
@Date  : 2025/8/21
@Desc  : 动态表名生成器，根据城市名或编码查询对应省份拼音替换表名
"""
from lib import logger
from typing import Dict, Optional

# 延迟导入，避免循环依赖
def get_db_cursor(name='main'):
    """获取数据库连接"""
    try:
        from lib.check.base import get_db_cursor as _get_db_cursor
        return _get_db_cursor(name)
    except ImportError:
        # 如果无法导入，创建简单的数据库连接
        from mod.db_op import DB
        from lib import main_database_conf
        host = main_database_conf['host']
        port = main_database_conf['port']
        dbname = main_database_conf['database']
        user = main_database_conf['user']
        password = main_database_conf['password']
        return DB(host, port, dbname, user, password)


class TableNameGenerator:
    """
    动态表名生成器
    
    根据城市名或城市编码查询dim_ref_comm_prov_city表，
    获取对应的省份拼音，用于动态生成表名
    """
    
    def __init__(self):
        self._cache: Dict[str, str] = {}  # 缓存城市标识符到省份拼音的映射
        self._cache_loaded: bool = False
        
    def _load_city_province_mapping(self) -> None:
        """
        从数据库加载城市-省份映射关系并缓存
        """
        if self._cache_loaded:
            return
            
        try:
            db = get_db_cursor('main')
            
            # 查询所有城市和省份的映射关系
            sql = """
                SELECT city_code, city_name, city_pinyin, 
                       province_code, province_name, province_pinyin
                FROM dim_ref_comm_prov_city
                WHERE province_pinyin IS NOT NULL
            """
            
            result = db.query_sql(sql)
            db.close()
            
            # 构建缓存：支持城市名、城市编码、城市拼音查询
            for _, row in result.iterrows():
                province_pinyin = row['province_pinyin']
                
                # 城市名映射
                if row['city_name']:
                    self._cache[row['city_name']] = province_pinyin
                    
                # 城市编码映射
                if row['city_code']:
                    self._cache[row['city_code']] = province_pinyin
                    
                # 城市拼音映射
                if row['city_pinyin']:
                    self._cache[row['city_pinyin']] = province_pinyin
                    
                # 省份名映射
                if row['province_name']:
                    self._cache[row['province_name']] = province_pinyin
                    
                # 省份编码映射
                if row['province_code']:
                    self._cache[row['province_code']] = province_pinyin
            
            self._cache_loaded = True
            logger.info(f"城市-省份映射加载完成，共{len(self._cache)}条记录")
            
        except Exception as e:
            logger.error(f"加载城市-省份映射失败: {e}")
            raise
    
    def _get_province_pinyin(self, city_identifier: str) -> str:
        """
        根据城市标识符获取省份拼音
        
        Args:
            city_identifier: 城市名、城市编码或城市拼音
            
        Returns:
            省份拼音，如果找不到则返回'chongqing'作为默认值
        """
        # 确保缓存已加载
        self._load_city_province_mapping()
        
        # 直接查找
        if city_identifier in self._cache:
            return self._cache[city_identifier]
        
        # 模糊匹配：去除"市"、"区"、"县"等后缀
        for suffix in ['市', '区', '县', '自治区', '自治州', '自治县']:
            if city_identifier.endswith(suffix):
                clean_name = city_identifier[:-len(suffix)]
                if clean_name in self._cache:
                    return self._cache[clean_name]
        
        # 如果找不到，记录警告并返回默认值
        logger.warning(f"未找到城市'{city_identifier}'对应的省份拼音，使用默认值'chongqing'")
        return 'chongqing'
    
    def get_table_name(self, base_table_name: str, city_identifier: str) -> str:
        """
        根据基础表名和城市标识符生成完整表名
        
        Args:
            base_table_name: 基础表名（不包含省份拼音后缀）
            city_identifier: 城市名、城市编码或城市拼音
            
        Returns:
            完整的表名
            
        Examples:
            >>> generator = TableNameGenerator()
            >>> generator.get_table_name("zxgk_basic_data_water_year_report", "九龙坡区")
            'zxgk_basic_data_water_year_report_chongqing'
            >>> generator.get_table_name("zxgk_basic_data_water_year_report", "500107000")
            'zxgk_basic_data_water_year_report_chongqing'
        """
        try:
            province_pinyin = self._get_province_pinyin(city_identifier)
            table_name = f"{base_table_name}_{province_pinyin}"
            
            logger.debug(f"生成表名: {base_table_name} + {city_identifier} -> {table_name}")
            return table_name
            
        except Exception as e:
            logger.error(f"生成表名失败: base_table_name={base_table_name}, "
                        f"city_identifier={city_identifier}, error={e}")
            # 发生错误时返回默认的chongqing表名
            return f"{base_table_name}_chongqing"
    
    def clear_cache(self) -> None:
        """
        清除缓存，强制重新加载城市-省份映射
        """
        self._cache.clear()
        self._cache_loaded = False
        logger.info("城市-省份映射缓存已清除")
    
    def get_cache_info(self) -> Dict[str, any]:
        """
        获取缓存信息，用于调试
        
        Returns:
            包含缓存状态和统计信息的字典
        """
        return {
            'cache_loaded': self._cache_loaded,
            'cache_size': len(self._cache),
            'sample_mappings': dict(list(self._cache.items())[:5]) if self._cache else {}
        }


# 全局实例，供其他模块使用
table_name_generator = TableNameGenerator()


def get_table_name(base_table_name: str, city_identifier: str) -> str:
    """
    便捷函数：生成表名
    
    Args:
        base_table_name: 基础表名
        city_identifier: 城市标识符
        
    Returns:
        完整表名
    """
    return table_name_generator.get_table_name(base_table_name, city_identifier)
