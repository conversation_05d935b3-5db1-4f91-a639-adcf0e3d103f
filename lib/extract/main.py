# -*- coding: utf-8 -*-
"""
@File  : main.py
@Author: <PERSON>
@Date  : 2025/3/19 11:15
@Desc  : 
"""
import os
import re
import json
from collections import defaultdict
import pandas as pd

from lib.llm.llm import ask_llm
from lib.llm import API_KEY
from lib.llm.rag_llm import (
    get_token,
    add_chat_model,
    add_embed_model,
    select_model,
    create_rag_obj,
    remove_assistant,
    create_assistant,
    pipeline_docs,
    set_dialog,
    set_conversation,
    generate_conversation_id,
    chat
)
from lib.extract_from_file.step_2_extract_polluant_discharge_permit_bed import DocumetParser
from lib.check.base import clean_null
from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx


def get_rag_assistant(file1_path, file1_type, file2_path, file2_type):
    # 生成auth, token, tenant_id
    auth, token, tenant_id = get_token()
    print('auth', auth)
    print('token', token)
    print('tenant_id', tenant_id)
    # 注册模型
    response = add_embed_model(auth)
    print(response)
    response = add_chat_model(auth, api_key=API_KEY)
    print(response)
    response = select_model(auth, tenant_id)
    print(response)
    rag_obj = create_rag_obj(api_key=token)
    dataset1 = pipeline_docs(rag_obj, file1_type, file1_path)
    dataset2 = pipeline_docs(rag_obj, file2_type, file2_path)
    # class dataset1: id = "7deeb2d4060011f0928a0242ac130006"
    # class dataset2: id = "8485aada060011f08e1f0242ac130006"
    print('dataset1', dataset1.id)
    print('dataset2', dataset2.id)
    remove_assistant(rag_obj)
    # env_assistant = create_assistant('env', rag_obj, dataset1.id)
    # env_session = env_assistant.create_session('conversation')
    # exe_assistant = create_assistant('exe', rag_obj, dataset2.id)
    # exe_session = exe_assistant.create_session('conversation')
    return auth, dataset1, dataset2


def ask_rag(assistant, question, cache=False):
    """ 向rag(llm)提问，cache表示是否考虑历史对话记录 """
    def get_session():
        for session in assistant.list_sessions():
            if session.name == 'temp':
                if cache:
                   return session
                else:
                    assistant.delete_sessions(ids=[session.id])
                    return assistant.create_session('temp')
        return assistant.create_session('temp')

    session = get_session()
    full_ans = ''
    for ans in session.ask(question, stream=True):
        full_ans = ans.content
        # print(full_ans)
        # print('-' * 100)
    return full_ans


def ask_rag_full(auth, dataset_id, question):
    """ 向rag(llm)提问完整回复 """
    dialog_id = set_dialog(auth, dataset_id)
    conversation_id = generate_conversation_id()
    session = set_conversation(auth, dialog_id, conversation_id)
    ans = chat(auth, conversation_id, question)
    return ans


# env_assistant, exe_assistant = get_rag_assistant()
# ans = ask_rag(env_assistant, '关键工序窄口拓宽项目的建设单位联系人是谁？')
# print('-'*100, '\n', ans)
# ans = ask_rag(env_assistant, '关键工序窄口拓宽项目投资了多少钱？')
# print('-'*100, '\n', ans)


# print("\n==================== AI =====================\n")
# print("你好！ 我是你的助理，有什么可以帮到你的吗？")
#
# while True:
#     question = input("\n==================== User =====================\n> ")
#     print("\n==================== AI =====================\n")
#
#     cont = ""
#     full_ans = ""
#     for ans in env_session.ask(question, stream=True):
#         full_ans = ans.content
#         print(full_ans)
#         print('-' * 100)


def extract_json_from_txt(txt):
    """ 从大模型回答中提取json结构 """
    pattern = r"```json(.*?)```"
    try:
        match = re.findall(pattern, txt, re.DOTALL)[-1]
        ans = json.loads(match)
    except:
        ans = None
    return ans


def convert_to_dict(obj):
    if isinstance(obj, defaultdict):  # 如果是 defaultdict 类型
        return {key: convert_to_dict(value) for key, value in obj.items()}
    elif isinstance(obj, list):  # 如果是列表类型，递归处理每个元素
        return [convert_to_dict(item) for item in obj]
    elif isinstance(obj, dict):  # 如果是普通 dict，递归处理每个键值对
        return {key: convert_to_dict(value) for key, value in obj.items()}
    else:  # 如果是其他类型，直接返回
        return obj


def add_space(s):
    # 匹配数字（包括小数）后接字母或汉字的情况
    pattern = r'(\d+\.?\d*)([A-Za-z\u4e00-\u9fff])'
    # 在数字和字母/汉字之间插入空格
    return re.sub(pattern, r'\1 \2', s)


def json_val_add_space(data):
    if isinstance(data, str):
        data = add_space(data)
    elif isinstance(data, list):
        for idx, item in enumerate(data):
            data[idx] = json_val_add_space(item)
    elif isinstance(data, dict):
        for key, val in data.items():
            data[key] = json_val_add_space(val)

    return data


def link_pipeline(file1_path, file1_type, file1_name, file2_path, file2_type, file2_name):
    """ 大模型构图 """

    auth, dataset1, dataset2 = get_rag_assistant(file1_path, file1_type, file2_path, file2_type)
    # 大模型确定要点key
    q_key = """
针对建设项目环境影响评价报告、企业排污许可证中普遍应该包含的重要信息，请列举出最重要的10个关键信息的键(key)的数组。
最终答案请用json格式输出，并以```json开始，以```结束，下面给出了3个关键信息的键示例：
```json
["项目废气排放口数量", "项目废水排放口数量", "项目排放的主要污染物"]
```
""".strip('\n')
    keys = ask_llm(q_key)
    print(keys)
    keys = extract_json_from_txt(keys)
    print(keys)

    # 大模型从文档1提取要点value
    q2 = """
基于"{project}"请给出"{key}"，结果用json格式的key-value键值对表示，值请用一句话或一段话或数字表示，如果没有"{key}"，
值value请写空字符串，键key请保留，即输出json对象`{{"{key}": ""}}`，最终答案请以```json开始，以```结束。
## 示例
```json
{{
    "项目总投资额": "500万"
}}
```
## 请回答
{{"{key}": <答案>}}
""".strip('\n')
    key_info1 = {}
    for key in keys:
        _q2 = q2.format(project=file1_name, key=key)
        # ans = ask_rag(env_dataset, _q2)
        ans = ask_rag_full(auth, dataset1.id, _q2)
        print(ans)
        ans = extract_json_from_txt(ans)
        if ans and key in ans and ans[key]:
            key_info1.update(ans)
        print(ans)
        print('-'*100)

    # 大模型从文档2提取要点value
    q3 = q2
    key_info2 = {}
    for key in keys:
        _q3 = q3.format(project=file2_name, key=key)
        # ans = ask_rag(exe_dataset, _q3)
        ans = ask_rag_full(auth, dataset2.id, _q3)
        print(ans)
        ans = extract_json_from_txt(ans)
        if ans and key in ans and ans[key]:
            key_info2.update(ans)
        print(ans)
        print('-'*100)

    # 提取共同的键
    union = set(key_info1.keys()) & set(key_info2.keys())
    same_key1, same_key2 = {}, {}
    for key in union:
        same_key1[key] = key_info1[key]
        same_key2[key] = key_info2[key]
        del key_info1[key]
        del key_info2[key]

    # 大模型提取相同点
    q4 = """
下面给出了2组某项目的环评报告或排污许可证中的关键信息，每组都是一个json对象表示的键值对，请判断两组json键key对应的值value
是否表达的是相同的含义，请将具有相同含义的键和值输出，值若表述方式不同，请将其统一。输出结果用json格式，如不存在任何一个
相同含义的键值，则输出空json对象。
## 示例
### 输入
组1
{{
    "项目废水排放口数量": "5",
    "项目总投资额": "300万"
}}
组2
{{
    "项目废水排放口数量": "3",
    "项目总投资额": "3,000,000元"
}}
### 输出
```json
{{
    "项目总投资额": "300万元"
}}
```
## 真实
### 输入
组1
{group1}
组2
{group2}
### 输出
""".strip('\n')
    _q4 = q4.format(group1=json.dumps(same_key1, ensure_ascii=False), group2=json.dumps(same_key2, ensure_ascii=False))
    ans = ask_llm(_q4)
    same = extract_json_from_txt(ans)
    if not same:
        same = {}
    for key in union:
        if key not in same:
            key_info1[key] = same_key1[key]
            key_info2[key] = same_key2[key]

    return same, key_info1, key_info2


def ent_link_pipeline(file1_path, file1_type, file1_name, file2_path, file2_type, file2_name):
    """ 提取环评报告中的第三级目录 """
    assert file1_type in ["环评", "许可证"] and file2_type in ["环评", "许可证"] and file1_type != file2_type, \
        "文件类型错误，文件1类型为{}，文件2类型为{}".format(file1_type, file2_type)
    auth, dataset1, dataset2 = get_rag_assistant(file1_path, file1_type, file2_path, file2_type)
    if file1_type == "环评":
        ent_name = file1_name
        ent_dataset = dataset1
        licence_name = file2_name
        licence_dataset = dataset2
        licence_path = file2_path
    else:
        ent_name = file2_name
        ent_dataset = dataset2
        licence_name = file1_name
        licence_dataset = dataset1
        licence_path = file1_path

    # 加载排污许可证
    doc = DocumetParser(os.path.join(licence_path, 'data', licence_name))

    result = {
        '许可证': {
            '大气污染物排放': {
                '排放口': defaultdict(dict),
                '大气排放总许可量': defaultdict(dict),
                '排放许可限值': defaultdict(dict),
            },
            '水污染物排放': {
                '排放口': defaultdict(dict),
                '全厂排放口总计-年排放量限值': defaultdict(dict),
                '排放许可限值': defaultdict(dict)
            }
        },
        '环评': {
            '废气': {
                '废气排放口基本情况': defaultdict(dict),
                '废气排放总量': defaultdict(dict),
                '废气污染物排放量': defaultdict(dict)
            },
            '废水': {
                '废水排放口基本情况': defaultdict(dict),
                '废水排放总量': defaultdict(dict),
                '废水污染物排放量': defaultdict(dict)
            }
        },
        '排污单位': '',
        '异常': []
    }

    def ask_many_times(question, times=3):
        for i in range(times):
            ans = ask_rag_full(auth, ent_dataset.id, question)
            ans = extract_json_from_txt(ans)
            if ans:
                return ans
        return None


    # 提取排污单位（许可证）
    table = doc.find_table_by_name('排污单位基本信息表')
    name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
    if isinstance(name, str) and name != 'NotFound':
        result['排污单位'] = f'{name}（排污许可证）'
    else:
        result['排污单位'] = ''

    # 环评大气排气筒高度、内径、名称、数量
    q1 = """
请问"{file_name}"中是否说明了大气污染物排放的排气筒信息，如果涉及请给出所有大气排气筒的数量以及每个排气筒的名称、高度、内径，如果有编码请确保编码完整，
最终结果请用json格式给出，并以```json开始，以```结束。没有排气筒时请输出空数组"[]"。示例如下：
#### 有排气筒输出示例
```json
[
    {{
        "大气排气筒名称": "熔化废气排放口（DA001）",
        "大气排气筒高度": "18 m",
        "大气排气筒内径": "1.2 m"
    }},{{
        "大气排气筒名称": "收尘系统排气筒（DA002）",
        "大气排气筒高度": "10 m",
        "大气排气筒内径": "2 m"
    }}
]
```

#### 无排气筒输出示例
```json
[]
```
""".lstrip('\n')
    _q1 = q1.format(file_name=ent_name)
    ent_gas_outlets = ask_many_times(_q1)

    # 许可证大气排气筒高度、内径、名称、数量
    table = doc.find_table_by_name("大气排放口基本情况表")
    if table is not None:
        licence_gas_outlets = doc.get_table_index_by_name(table, "序号", col_select=False)
        licence_gas_outlets = [row.split('|') for row in licence_gas_outlets.split('\n')]
        licence_gas_outlets = pd.DataFrame(licence_gas_outlets, columns=[
            "序号","排放口编号","排放口名称","污染物种类","经度","纬度","排气筒高度","排气筒内径","排气温度","其他信息"
        ]).astype(str)
        licence_gas_outlets['排放口'] = licence_gas_outlets['排放口名称'] + "（" + licence_gas_outlets['排放口编号'] + "）"
        licence_gas_outlets = licence_gas_outlets[['排放口', '排气筒高度', '排气筒内径']].copy()

        if not licence_gas_outlets.empty:
            result_outlets = result['许可证']['大气污染物排放']['排放口']
            result_outlets['排放口数量'] = len(licence_gas_outlets)
            result_outlets['排气筒高度'] = json_val_add_space(licence_gas_outlets['排气筒高度'].tolist())
            result_outlets['排气筒内径'] = json_val_add_space(licence_gas_outlets['排气筒内径'].tolist())
            result_outlets['排放口名称'] = json_val_add_space(licence_gas_outlets['排放口'].tolist())
        if ent_gas_outlets:
            ent_gas_outlets_df = pd.DataFrame(ent_gas_outlets)
            result_outlets = result['环评']['废气']['废气排放口基本情况']
            result_outlets['排放口数量'] = len(ent_gas_outlets_df)
            result_outlets['排气筒高度'] = json_val_add_space(ent_gas_outlets_df['大气排气筒高度'].tolist())
            result_outlets['排气筒内径'] = json_val_add_space(ent_gas_outlets_df['大气排气筒内径'].tolist())
            result_outlets['排放口名称'] = json_val_add_space(ent_gas_outlets_df['大气排气筒名称'].tolist())

    q1_compare = """
下面给出了两组大气排放筒的名称、高度、内径信息，每组有{cnt}个，忽略顺序和表达方式和排气筒名称的差异，
请你判断两组排气筒的高度、内径数据是否一致，只需要给出不一样项目名称。最终结果请用json格式给出，并以```json开始，以```结束。
没有差异时请回答空数组"[]"。你的答案只可能是[]或者["排气筒高度"]或者["排气筒内径"]或者["排气筒高度", "排气筒内径"]，
下面给出了无差异和有差异的情况及回答
#### 无差异数据示例
##### 组1
{{
    "排放口名称": ["收尘系统排气筒", "DW002"],
    "排气筒内径": ["1.3 m", "1.5 m"],
    "排气筒高度": ["16 m", "18 米"]
}}

##### 组2
{{
    "排放口名称": ["烟囱排放口", "DW001"],
    "排气筒内径": ["1.5 米", "1.3 m"],
    "排气筒高度": [18, 16]
}}

##### 示例回答
```json
[]
```

#### 有差异数据示例
##### 组1
{{
    "排放口名称": ["收尘系统排气筒", "DW002"],
    "排气筒内径": ["1.3 m", "1.5 m"],
    "排气筒高度": ["16 m", "18 米"]
}}

##### 组2
{{
    "排放口名称": ["烟囱排放口", "DW001"],
    "排气筒内径": ["1.4 米", "1.3 m"],
    "排气筒高度": [18, 16]
}}

##### 示例回答
```json
["排气筒内径"]
```

#### 真实数据
##### 组1
{group1}

##### 组2
{group2}
""".lstrip('\n')

    if not licence_gas_outlets.empty and ent_gas_outlets:
        if len(licence_gas_outlets) != len(ent_gas_outlets_df):
            keys = ['排放口数量']
        else:
            _q1_compare = q1_compare.format(
                cnt=len(licence_gas_outlets),
                group1=json.dumps(licence_gas_outlets.to_dict(orient='list'), ensure_ascii=False),
                group2=json.dumps(ent_gas_outlets_df.to_dict(orient='list'), ensure_ascii=False)
            )
            keys = ask_llm(_q1_compare)
            print(keys)
            keys = extract_json_from_txt(keys) or []
        for key in keys:
            result['异常'].append({
                "许可证": ['大气污染物排放', '排放口', key],
                "环评": ['废气', '废气排放口基本情况', key],
                "规则": "许可证记载的污染物排放口信息与环境影响评价报告不符"
            })

    # 环评水排放口名称、数量
    q2 = """
请问"{file_name}"中是否说明了水污染物排放口信息，如果涉及请给出所有水污染物排放口的的名称，不包括雨水排放口，如果有编码请确保编码完整，
最终结果请用json格式给出，并以```json开始，以```结束。没有废水排放口时请输出空数组"[]"。示例如下：
#### 有水污染物排放口输出示例
```json
["生活污水排放口(DW001)"]
```

#### 无水污染物排放口输出示例
```json
[]
```
""".lstrip('\n')
    _q2 = q2.format(file_name=ent_name)
    ent_water_outlets = ask_many_times(_q2)

    # 许可证水排放口名称、数量
    table = doc.find_table_by_name("废水间接排放口基本情况表")
    if table is not None:
        licence_water_outlets = doc.get_table_index_by_name(table, "序号", col_select=False)
        licence_water_outlets = [row.split('|') for row in licence_water_outlets.split('\n')]
        licence_water_outlets = pd.DataFrame(licence_water_outlets, columns=[
            "序号", "排放口编号", "排放口名称", "经度", "纬度", "排放去向", "排放规律", "间歇排放时段", "名称",
            "污染物种类", "排水协议规定的浓度限值", "国家或地方污染物排放标准浓度限值"
        ]).astype(str)
        licence_water_outlets['排放口'] = licence_water_outlets['排放口名称'] + "（" + licence_water_outlets['排放口编号'] + "）"
        licence_water_outlets = licence_water_outlets[['排放口']].copy()
        licence_water_outlets.drop_duplicates(inplace=True)

        if not licence_water_outlets.empty:
            result_outlets = result['许可证']['水污染物排放']['排放口']
            result_outlets['排放口数量'] = len(licence_water_outlets)
            result_outlets['排放口名称'] = licence_water_outlets['排放口'].tolist()
        if ent_water_outlets:
            ent_water_outlets_df = pd.DataFrame(ent_water_outlets)
            result_outlets = result['环评']['废水']['废水排放口基本情况']
            result_outlets['排放口数量'] = len(ent_water_outlets_df)
            result_outlets['排放口名称'] = ent_water_outlets
        if not licence_water_outlets.empty and ent_water_outlets:
            if len(licence_water_outlets) != len(ent_water_outlets_df):
                result['异常'].append({
                    "许可证": ['水污染物排放', '排放口', '排放口数量'],
                    "环评": ['废水', '废水排放口基本情况', '排放口数量'],
                    "规则": "许可证记载的污染物排放口信息与环境影响评价报告不符"
                })

    # 环评排污单位大气排放总许可量
    q3 = """
请问"{file_name}"中是否说明了每种大气污染物的年排放总量限值，不一定会直接说明总量限值，可能需要你充分理解后判断，
单位一般为t/a，如果涉及请给出每种污染物的的年排放总量限值（非零），最终结果请用json格式给出，
并以```json开始，以```结束。没有提及时时请输出空对象"{{}}"。示例如下：
#### 有大气污染物年排放总量限值输出示例
```json
{{
    "颗粒物": "0.236 t/a",
    "SO2": "0.01 t/a",
    "NOx": "0.22 t/a"
}}
```

#### 无大气污染物年排放总量限值输出示例
```json
{{}}
```
""".lstrip('\n')
    _q3 = q3.format(file_name=ent_name)
    ent_gas_thres = ask_many_times(_q3)

    # 排污许可证大气排放总许可量
    table = doc.find_table_by_name("企业大气排放总许可量")
    licence_gas_thres = doc.get_table_index_by_name(table, "序号", col_select=False)
    licence_gas_thres = [row.split('|') for row in licence_gas_thres.split('\n')]
    licence_gas_thres = pd.DataFrame(licence_gas_thres, columns=[
        "序号", "污染物种类", "第一年（t/a）", "第二年（t/a）", "第三年（t/a）", "第四年（t/a）", "第五年（t/a）",
    ]).astype(str)
    licence_gas_thres['总许可量'] = licence_gas_thres['第一年（t/a）']
    licence_gas_thres = licence_gas_thres[['污染物种类', '总许可量']].copy()

    q3_compare = """
下面给出了排污许可证与环评报告中大气污染物的年排放总量限值，其中同一种污染物表述方式可能不同（例如SO2与二氧化硫），
请你进行如下判断，回答'yes'或者'no'，必须同时满足如下全部要求才回答'yes'，否则回答'no'。

### 要求
1. 排污许可证与环评中至少存在一个相同的污染物；
2. 至少存在一个相同污染物排放总量限值在排污许可证与环评中均不为“/”，并且给出了数值；
3. 满足“要求1、2”的所有相同污染物中存在至少一个排污许可证限值大于环评限值；

### 示例如下
#### 示例1
##### 排污许可证
{{
    "非甲烷总烃": "0.169 t/a",
    "二氧化硫": "/"
}}

##### 环评
{{
    "SO2": "0.23",
    "颗粒物": "/"
}}

##### 回答
'no'

#### 示例2
##### 排污许可证
{{
    "非甲烷总烃": "0.169 t/a",
    "二氧化硫": "0.24 t/a"
}}

##### 环评
{{
    "SO2": "0.23",
    "颗粒物": "/"
}}

##### 回答
'yes'

#### 示例3
##### 排污许可证
{{
    "非甲烷总烃": "0.169 t/a",
    "二氧化硫": "0.22 t/a"
}}

##### 环评
{{
    "SO2": "0.23",
    "颗粒物": "/"
}}

##### 回答
'no'

#### 真实数据
##### 排污许可证
{permit}

##### 环评
{ent}
""".lstrip('\n')

    if not licence_gas_thres.empty:
        result_permit = result['许可证']['大气污染物排放']
        result_permit['大气排放总许可量'] = json_val_add_space(licence_gas_thres.set_index('污染物种类')['总许可量'].to_dict())
    if ent_gas_thres:
        result_permit = result['环评']['废气']
        result_permit['废气排放总量']  = json_val_add_space(ent_gas_thres)
    if not licence_gas_thres.empty and ent_gas_thres:
        licence_gas_thres = licence_gas_thres.set_index('污染物种类')['总许可量'].to_dict()
        _q3_compare = q3_compare.format(
            permit=json.dumps(licence_gas_thres, ensure_ascii=False),
            ent=json.dumps(ent_gas_thres, ensure_ascii=False)
        )
        ans = ask_llm(_q3_compare)
        print(ans)
        idx = ans.find('</think>')
        ans = ans[idx+8:] if idx != -1 else ans
        if 'yes' in ans:
            result['异常'].append({
                "许可证": ['大气污染物排放', '大气排放总许可量'],
                "环评": ['废气', '废气排放总量'],
                "规则": "许可证记载的污染物排放限值与环境影响评价报告记载的排放标准限值不符"
            })

    # 环评排污单位水排放许可限值
    q4 = """
请问"{file_name}"中是否说明了每种水污染物的年排放总量限值（**所有排口合计**），不一定会直接说明总量限值，可能需要你充分理解后判断，
单位一般为t/a，如果涉及请只列出每种污染物的年排放总量限值（非零），没有值的不要列出来，最终结果请用json格式给出，
并以```json开始，以```结束。没有提及时请输出空对象"{{}}"。示例如下：
#### 有水污染物年排放总量限值输出示例
```json
{{
    "COD": "0.32 t/a",
    "SS": "0.16 t/a"
}}
```

#### 无水污染物年排放总量限值输出示例
```json
{{}}
```
""".lstrip('\n')
    _q4 = q4.format(file_name=ent_name)
    ent_water_thres = ask_many_times(_q4)

    # 许可证排污单位水排放许可限值
    table = doc.find_table_by_name("废水污染物排放")
    licence_water_thres = doc.get_table_index_by_name(table, "全厂排放口总计", col_select=True)
    licence_water_thres = [row.split('|') for row in licence_water_thres.split('\n')]
    licence_water_thres = pd.DataFrame(licence_water_thres, columns=[
        "污染物种类", "第一年（t/a）", "第二年（t/a）", "第三年（t/a）", "第四年（t/a）", "第五年（t/a）",
    ]).astype(str)
    licence_water_thres['总许可量'] = licence_water_thres['第一年（t/a）']
    licence_water_thres = licence_water_thres[['污染物种类', '总许可量']].copy()

    q4_compare = """
下面给出了排污许可证与环评报告中废水污染物的年排放总量限值，其中同一种污染物表述方式可能不同（例如化学需氧量与COD），
请你进行如下判断，回答'yes'或者'no'，必须同时满足如下全部要求才回答'yes'，否则回答'no'。

### 要求
1. 排污许可证与环评中至少存在一个相同的污染物；
2. 至少存在一个相同污染物排放总量限值在排污许可证与环评中均不为“/”，并且给出了数值；
3. 满足“要求1、2”的所有相同污染物中存在至少一个排污许可证限值大于环评限值；

### 示例如下
#### 示例1
##### 排污许可证
{{
    "氨氮"："0.169 t/a",
    "COD": "/"
}}

##### 环评
{{
    "化学需氧量"："0.23",
    "pH值": "/"
}}

##### 回答
'no'

#### 示例2
##### 排污许可证
{{
    "氨氮"："0.169 t/a",
    "COD": "0.26 t/a"
}}

##### 环评
{{
    "化学需氧量"："0.23",
    "pH值": "/"
}}

##### 回答
'yes'

#### 示例3
##### 排污许可证
{{
    "氨氮"："0.169 t/a",
    "COD": "0.21 t/a"
}}

##### 环评
{{
    "化学需氧量"："0.23",
    "pH值": "/"
}}

##### 回答
'no'

#### 真实数据
##### 排污许可证
{permit}

##### 环评
{ent}
""".lstrip('\n')

    if not licence_water_thres.empty:
        result_permit = result['许可证']['水污染物排放']
        result_permit['全厂排放口总计-年排放量限值'] = json_val_add_space(licence_water_thres.set_index('污染物种类')['总许可量'].to_dict())
    if ent_water_thres:
        result_permit = result['环评']['废水']
        result_permit['废水排放总量']  = json_val_add_space(ent_water_thres)
    if not licence_water_thres.empty and ent_water_thres:
        licence_water_thres = licence_water_thres.set_index('污染物种类')['总许可量'].to_dict()
        _q4_compare = q4_compare.format(
            permit=json.dumps(licence_water_thres, ensure_ascii=False),
            ent=json.dumps(ent_water_thres, ensure_ascii=False)
        )
        ans = ask_llm(_q4_compare)
        print(ans)
        idx = ans.find('</think>')
        ans = ans[idx+8:] if idx != -1 else ans
        if 'yes' in ans:
            result['异常'].append({
                "许可证": ['水污染物排放', '全厂排放口总计-年排放量限值'],
                "环评": ['废水', '废水排放总量'],
                "规则": "许可证记载的污染物排放限值与环境影响评价报告记载的排放标准限值不符"
            })

    # 环评大气排口限值
    q5 = """
请问"{file_name}"中是否有每个废气排放口的每种废气污染物的排放浓度要求和年排放总量要求（单位一般为t/a），
要求不一定直接说明，可能用不同的表述方式说明了污染物的排放浓度要求和年排放总量要求，需要你充分理解材料后判断，
如果说明了请分排放口列出每种污染物的排放浓度限值和年排放总量限值，最终结果请用json格式给出，
并以```json开始，以```结束。没有时请输出空对象"{{}}"。示例如下：
#### 有排放口及污染物限值时输出示例
```json
{{
    "热风炉废气排放口（DA001）": {{
        "氮氧化物": {{
            "排放浓度限值": "200 mg/Nm3",
            "年排放量限值": "0.13 t/a"
        }},
        "颗粒物": {{
            "排放浓度限值": "50 mg/Nm3",
            "年排放量限值": "0.53 t/a"
        }}
    }},
    "炒制废气废气排放口（DA002）": {{
        "氮氧化物": {{
            "排放浓度限值": "200 mg/Nm3",
            "年排放量限值": "0.13 t/a"
        }},
        "颗粒物": {{
            "排放浓度限值": "100 mg/Nm3",
            "年排放量限值": "0.86 t/a"
        }}
    }}
}}
```

#### 无排放口及污染物限值时输出示例
```json
{{}}
```
""".lstrip('\n')
    _q5 = q5.format(file_name=ent_name)
    ent_gas_thres = ask_many_times(_q5)

    # 许可证大气排口限值
    table = doc.find_table_by_name("大气污染物有组织排放")
    licence_gas_thres = doc.get_table_index_by_name(table, "一般排放口", col_select=False)
    licence_gas_thres = [row.split('|') for row in licence_gas_thres.split('\n')]
    licence_gas_thres = pd.DataFrame(licence_gas_thres, columns=[
        "序号", "排放口编号", "排放口名称", "污染物种类", "许可排放浓度限值", "许可排放速率限值", "第一年", "第二年",
        "第三年", "第四年", "第五年", "承诺更加严格排放浓度限值"
    ]).astype(str)
    licence_gas_thres['排放口'] = licence_gas_thres['排放口名称'] + '（' + licence_gas_thres['排放口编号'] + '）'
    licence_gas_thres.rename(columns={"第一年": "许可年排放量限值"}, inplace=True)
    licence_gas_thres = licence_gas_thres[['排放口', '污染物种类', '许可排放浓度限值', '许可年排放量限值']].copy()
    tmp = {}
    for outlet, group1 in licence_gas_thres.groupby('排放口'):
        tmp[outlet] = {}
        for _, row in group1.iterrows():
            tmp[outlet][row['污染物种类']] = {
                " 许可排放浓度限值": row['许可排放浓度限值'],
                "许可年排放量限值": row['许可年排放量限值']
            }
    licence_gas_thres = tmp

    _q5_compare = """
下面给出了排污许可证与环评报告中废气污染物分排口的`排放浓度限值`与`年排放量限值`，其中同一个排口可能有不同的中文表述方式，
编码相同的排口认为是同一个排口，同一种污染物表述方式可能不同（例如SO2与二氧化硫），
请你逐一判断许可证与环评的单个排口的单项污染物是否同时满足下述全部条件，若某个排口的某项污染物同时满足下述全部条件，
则分条列举出对应的排口及污染物，不要遗漏任何排口和污染物。最终结果请用json格式给出，并以```json开始，以```结束。
若没有满足条件的排口及污染物，则输出空数组"[]"。

### 要求
1. 排污许可证与环评报告中存在对应的排放口；
2. 相同排放口下存在相同的污染物；
3. 满足“要求1、2”的污染物“排放浓度限值”和“年排放量限值”两项限值至少有一个在许可证和环评中都同时有具体数值；
4. 满足“要求1、2、3”的污染物“排放浓度限值”或者“年排放量限值”在许可证中的限值大于环评中的限值；

#### 示例
##### 许可证
{{
    "热风炉废气排放口（DA001）": {{
        "氮氧化物": {{
            "许可排放浓度限值": "200 mg/Nm3",
            "许可年排放量限值": "0.1 t/a"
        }},
        "颗粒物": {{
            "许可排放浓度限值": "40 mg/Nm3",
            "许可年排放量限值": "0.43 t/a"
        }},
        "二氧化硫": {{
            "许可排放浓度限值": "150 mg/Nm3",
            "许可年排放量限值": "0.7 t/a"
        }}
    }},
    "炒制废气废气排放口（DA002）": {{
        "氮氧化物": {{
            "许可排放浓度限值": "200 mg/Nm3",
            "许可年排放量限值": "0.13 t/a"
        }},
        "颗粒物": {{
            "许可排放浓度限值": "100 mg/Nm3",
            "许可年排放量限值": "0.86 t/a"
        }}
    }}
}}

##### 环评
{{
    "DA003": {{
        "NOx": {{
            "排放浓度限值": "100 mg/Nm3",
            "年排放量限值": "0.1 t/a"
        }},
        "颗粒物": {{
            "排放浓度限值": "50 mg/Nm3",
            "年排放量限值": "0.53 t/a"
        }}
    }},
    "DA001": {{
        "NOx": {{
            "排放浓度限值": "100 mg/Nm3",
            "年排放量限值": "0.13 t/a"
        }},
        "颗粒物": {{
            "排放浓度限值": "/",
            "年排放量限值": "0.53 t/a"
        }},
		"SO2": {{
            "排放浓度限值": "/",
            "年排放量限值": "0.6 t/a"
        }}
    }}
}}


##### 回答
```json
[
    {{
        "许可证": ["热风炉废气排放口（DA001）", "氮氧化物"],
        "环评": ["DA001", "NOx"] 
    }},{{
        "许可证": ["热风炉废气排放口（DA001）", "二氧化硫"],
        "环评": ["DA001", "SO2"]
    }}
]
```

#### 真实数据
##### 许可证
{permit}

##### 环评
{ent}
""".lstrip('\n')

    if licence_gas_thres:
        result_permit = result['许可证']['大气污染物排放']
        result_permit['排放许可限值'] = json_val_add_space(licence_gas_thres)
    if ent_gas_thres:
        result_permit = result['环评']['废气']
        result_permit['废气污染物排放量'] = json_val_add_space(ent_gas_thres)
    if licence_gas_thres and ent_gas_thres:
        _q5_compare = _q5_compare.format(
            permit=json.dumps(licence_gas_thres, ensure_ascii=False),
            ent=json.dumps(ent_gas_thres, ensure_ascii=False)
        )
        ans = ask_llm(_q5_compare)
        print(ans)
        idx = ans.find('</think>')
        ans = ans[idx + 8:] if idx != -1 else ans
        ans = extract_json_from_txt(ans) or []
        for dct in ans:
            licence, ent = dct['许可证'], dct['环评']
            result['异常'].append({
                "许可证": ['大气污染物排放', '排放许可限值'] + licence,
                "环评": ['废气', '废气污染物排放量'] + ent,
                "规则": "许可证记载的污染物排放限值与环境影响评价报告记载的排放标准限值不符"
            })

    # 环评废水排口限值
    q5 = """
请问"{file_name}"中是否有每个废水排放口的每种废水污染物的排放浓度标准和年排放总量标准（单位一般为t/a），
标准不一定直接说明，可能用不同的表述方式说明了污染物的排放浓度要求和年排放总量要求，需要你充分理解材料后判断，
如果说明了请分排放口列出每种污染物的排放浓度限值和年排放总量限值，最终结果请用json格式给出，
并以```json开始，以```结束。没有时请输出空对象"{{}}"。示例如下：
#### 有排放口及污染物限值时输出示例
```json
{{
    "生产废水排放口（DW001）": {{
        "COD": {{
            "排放浓度限值": "200 mg/L",
            "年排放量限值": "1.3 t/a"
        }},
        "NH3-N": {{
            "排放浓度限值": "50 mg/L",
            "年排放量限值": "0.83 t/a"
        }}
    }},
    "生活污水排放口（DW002）": {{
        "COD": {{
            "排放浓度限值": "200 mg/Nm3",
            "年排放量限值": "0.13 t/a"
        }},
        "动植物油": {{
            "排放浓度限值": "100 mg/Nm3",
            "年排放量限值": "0.86 t/a"
        }}
    }}
}}
```

#### 无排放口及污染物限值时输出示例
```json
{{}}
```
""".lstrip('\n')
    _q5 = q5.format(file_name=ent_name)
    ent_water_thres = ask_many_times(_q5)

    # 许可证废水排口限值
    table = doc.find_table_by_name("废水污染物排放")
    licence_water_thres = doc.get_table_index_by_name(table, "一般排放口", col_select=False)
    licence_water_thres = [row.split('|') for row in licence_water_thres.split('\n')]
    licence_water_thres = pd.DataFrame(licence_water_thres, columns=[
        "序号", "排放口编号", "排放口名称", "污染物种类", "许可排放浓度限值", "第一年",
        "第二年", "第三年", "第四年", "第五年"
    ]).astype(str)
    licence_water_thres['排放口'] = licence_water_thres['排放口名称'] + '（' + licence_water_thres['排放口编号'] + '）'
    licence_water_thres.rename(columns={"第一年": "许可年排放量限值"}, inplace=True)
    licence_water_thres = licence_water_thres[['排放口', '污染物种类', '许可排放浓度限值', '许可年排放量限值']].copy()
    tmp = {}
    for outlet, group1 in licence_water_thres.groupby('排放口'):
        tmp[outlet] = {}
        for _, row in group1.iterrows():
            tmp[outlet][row['污染物种类']] = {
                "许可排放浓度限值": row['许可排放浓度限值'],
                "许可年排放量限值": row['许可年排放量限值']
            }
    licence_water_thres = tmp

    _q6_compare = """
下面给出了排污许可证与环评报告中废水污染物分排口的`排放浓度限值`与`年排放量限值`，其中同一个排口可能有不同的中文表述方式，
编码相同的排口认为是同一个排口，同一种污染物表述方式可能不同（例如化学需氧量与COD），
请你逐一判断许可证与环评的单个排口的单项污染物是否同时满足下述全部条件，若某个排口的某项污染物同时满足下述全部条件，
则分条列举出对应的排口及污染物，不要遗漏任何排口和污染物。最终结果请用json格式给出，并以```json开始，以```结束。
若没有满足条件的排口及污染物，则输出空数组"[]"。

### 要求
1. 排污许可证与环评报告中存在对应的排放口；
2. 相同排放口下存在相同的污染物；
3. 满足“要求1、2”的污染物“排放浓度限值”和“年排放量限值”两项限值至少有一个在许可证和环评中都同时有具体数值；
4. 满足“要求1、2、3”的污染物“排放浓度限值”或者“年排放量限值”在许可证中的限值大于环评中的限值；

#### 示例
##### 许可证
{{
    "生产废水排放口（DW001）": {{
        "化学需氧量": {{
            "许可排放浓度限值": "400 mg/L",
            "许可年排放量限值": "1.2 t/a"
        }},
        "氨氮": {{
            "许可排放浓度限值": "50 mg/L",
            "许可年排放量限值": "0.83 t/a"
        }},
        "动植物油": {{
            "许可排放浓度限值": "150 mg/L",
            "许可年排放量限值": "0.3 t/a"
        }}
    }},
    "生活污水排放口（DW002）": {{
        "化学需氧量": {{
            "许可排放浓度限值": "200 mg/L",
            "许可年排放量限值": "0.58 t/a"
        }},
        "氨氮": {{
            "许可排放浓度限值": "100 mg/L",
            "许可年排放量限值": "0.86 t/a"
        }}
    }}
}}

##### 环评
{{
    "DW003": {{
        "COD": {{
            "排放浓度限值": "100 mg/L",
            "年排放量限值": "0.1 t/a"
        }},
    }},
    "DW001": {{
        "COD": {{
            "排放浓度限值": "300 mg/L",
            "年排放量限值": "1.3 t/a"
        }},
        "NH3-N": {{
            "排放浓度限值": "/",
            "年排放量限值": "0.53 t/a"
        }},
        "动植物油": {{
            "排放浓度限值": "/",
            "年排放量限值": "0.5 t/a"
        }},
        "SS": {{
            "排放浓度限值": "500 mg/L",
            "年排放量限值": "0.53 t/a"
        }}
    }}
}}


##### 回答
```json
[
    {{
        "许可证": ["生产废水排放口（DW001）", "化学需氧量"],
        "环评": ["DW001", "COD"]
    }},{{
        "许可证": ["生产废水排放口（DW001）", "氨氮"],
        "环评": ["DW001", "NH3-N"]
    }}
]
```

#### 真实数据
##### 许可证
{permit}

##### 环评
{ent}
""".lstrip('\n')

    if licence_water_thres:
        result_permit = result['许可证']['水污染物排放']
        result_permit['排放许可限值'] = json_val_add_space(licence_water_thres)
    if ent_water_thres:
        result_permit = result['环评']['废水']
        result_permit['废水污染物排放量'] = json_val_add_space(ent_water_thres)
    if licence_water_thres and ent_water_thres:
        _q6_compare = _q6_compare.format(
            permit=json.dumps(licence_water_thres, ensure_ascii=False),
            ent=json.dumps(ent_water_thres, ensure_ascii=False)
        )
        ans = ask_llm(_q6_compare)
        print(ans)
        idx = ans.find('</think>')
        ans = ans[idx + 8:] if idx != -1 else ans
        ans = extract_json_from_txt(ans) or []
        for dct in ans:
            licence, ent = dct['许可证'], dct['环评']
            result['异常'].append({
                "许可证": ['水污染物排放', '排放许可限值'] + licence,
                "环评": ['废水', '废水污染物排放量'] + ent,
                "规则": "许可证记载的污染物排放限值与环境影响评价报告记载的排放标准限值不符"
            })

    result = convert_to_dict(result)
    result['许可证'] = clean_null(result['许可证'])
    result['环评'] = clean_null(result['环评'])
    return result


def filter_think(string):
    """ 从大模型回答中过滤think部分文字 """
    idx = string.find('</think>')
    ans = string[idx + 8:] if idx != -1 else string
    return ans


class Exe_Link_Pipeline:

    def __init__(self, file1_path, file1_type, file1_name, file2_path, file2_type, file2_name):
        assert file1_type in ["执行报告", "许可证"] and file2_type in ["执行报告", "许可证"] and file1_type != file2_type, \
            "文件类型错误，文件1类型为{}，文件2类型为{}".format(file1_type, file2_type)
        if file1_type == "执行报告":
            exe_path = file1_path
            exe_type = file1_type
            exe_name = file1_name
            licence_path = file2_path
            licence_type = file2_type
            licence_name = file2_name
        else:
            exe_path = file2_path
            exe_type = file2_type
            exe_name = file2_name
            licence_path = file1_path
            licence_type = file1_type
            licence_name = file1_name
        self.exe_path = os.path.join(exe_path, exe_name)
        self.licence_path = os.path.join(licence_path, licence_name)
        self.exe_doc = MarkdownConverterWithPythonDocx(self.exe_path)
        self.licence_doc = DocumetParser(self.licence_path)

    def get_template(self):
        result = {
            '许可证': {
                '大气污染物排放': {
                    '大气排放总许可量': defaultdict(dict),
                },
                '水污染物排放': {
                    '全厂排放口总计-年排放量限值': defaultdict(dict),
                }
            },
            '执行报告': {
                '废气': {
                    '全厂合计': defaultdict(dict),
                },
                '废水': {
                    '全厂排放': defaultdict(dict)
                }
            },
            '排污单位': '',
            '异常': []
        }
        return result

    def run(self):
        result = self.get_template()
        result = self.unit_ent_name(result)
        result = self.unit_year_emission(result)

        result = convert_to_dict(result)
        result['许可证'] = clean_null(result['许可证'])
        result['执行报告'] = clean_null(result['执行报告'])

        return result

    def unit_ent_name(self, result):
        doc = self.licence_doc
        table = doc.find_table_by_name('排污单位基本信息表')
        name = doc.get_table_index_by_name(table, '单位名称', col_select=True)
        if isinstance(name, str) and name != 'NotFound':
            name =  f'{name}'
        else:
            name = ''
        result['排污单位'] = name
        return result

    def unit_year_emission(self, result):
        """ 企业年排放量对比 """
        # 执行报告年排放量提取
        docx = self.exe_doc
        tables = docx.tables(format_='dataframe')
        gas = tables['1、实际排放量指报告执行期内实际排放量']
        gas = pd.DataFrame(gas[2:].values, columns=gas.iloc[1].tolist())
        gas_cols = ['排放口类型', '污染物', '年度合计']
        gas = gas[gas_cols]
        gas = gas[gas['排放口类型']=='全厂合计']
        exe_gas = gas[['污染物', '年度合计']].copy()

        water = tables['注：实际排放量指报告执行期内实际排放量']
        water = pd.DataFrame(water[2:].values, columns=water.iloc[1].tolist())
        water_cols = ['排放口类型', '污染物', '年度合计']
        water = water[water_cols]
        water = water[water['排放口类型'].isin(['全厂直接排放', '全厂间接排放'])]
        water = water.groupby(['污染物']).sum().reset_index()
        exe_water = water[['污染物', '年度合计']].copy()

        # 许可证年排放量提取
        docx = self.licence_doc
        table = docx.find_table_by_name("企业大气排放总许可量")
        licence_gas = docx.get_table_index_by_name(table, "序号", col_select=False)
        licence_gas = [row.split('|') for row in licence_gas.split('\n')]
        licence_gas = pd.DataFrame(licence_gas, columns=[
            "序号", "污染物种类", "第一年（t/a）", "第二年（t/a）", "第三年（t/a）", "第四年（t/a）", "第五年（t/a）",
        ]).astype(str)
        licence_gas['总许可量'] = licence_gas['第一年（t/a）']
        licence_gas = licence_gas[['污染物种类', '总许可量']].copy()

        table = docx.find_table_by_name("废水污染物排放")
        licence_water = docx.get_table_index_by_name(table, "全厂排放口总计", col_select=True)
        licence_water = [row.split('|') for row in licence_water.split('\n')]
        licence_water = pd.DataFrame(licence_water, columns=[
            "污染物种类", "第一年（t/a）", "第二年（t/a）", "第三年（t/a）", "第四年（t/a）", "第五年（t/a）",
        ]).astype(str)
        licence_water['总许可量'] = licence_water['第一年（t/a）']
        licence_water = licence_water[['污染物种类', '总许可量']].copy()

        # 统一污染物名称
        gas_name = exe_gas['污染物'].to_list() + licence_gas['污染物种类'].to_list()
        gas_name = list(set(gas_name))
        water_name = exe_water['污染物'].to_list() + licence_water['污染物种类'].to_list()
        water_name = list(set(water_name))

        prompt = """
    下面给出了一些污染物种类，其中同一种污染物可能有不同的表述（例如二氧化硫与SO2），请你仔细辨别其中哪些是同一种污染物，
    将其找出并给一个统一的名称，逐一列出对应的原始表述和统一名称，最终结果请用json格式给出，并以```json开始，以```结束。
    示例如下：
    #### 示例
    ##### 原始污染物种类
    ['五日生化需氧量', '粪大肠菌群数（个/L）', ' 二氧化硫 ', '总氮', 'PH', ' 总磷（以P计）', 'BOD5', 'pH值 ', 'pH', ' SO2', '总氮（以N计）']
    
    ##### 输出
    {{
        "五日生化需氧量": "BOD5",
        "BOD5": "BOD5",
        "二氧化硫 ": "SO2",
        " SO2": "SO2",
        "PH": "pH值",
        "pH值 ": "pH值",
        "pH": "pH值",
        "总氮（以N计）": "总氮（以N计）",
        "总氮": "总氮（以N计）"
    }}
    
    #### 实际污染物种类
    ##### 原始污染物种类
    {species}
    
    ##### 输出
    """.lstrip('\n')
        gas_map = ask_llm(prompt.format(species=gas_name))
        print(gas_map)
        gas_map = filter_think(gas_map)
        gas_map = extract_json_from_txt(gas_map) or {}

        water_map = ask_llm(prompt.format(species=water_name))
        print(water_map)
        water_map = filter_think(water_map)
        water_map = extract_json_from_txt(water_map) or {}

        exe_gas['污染物'] = exe_gas['污染物'].replace(gas_map)
        licence_gas['污染物种类'] = licence_gas['污染物种类'].replace(gas_map)
        exe_water['污染物'] = exe_water['污染物'].replace(water_map)
        licence_water['污染物种类'] = licence_water['污染物种类'].replace(water_map)

        if not exe_gas.empty:
            key = result['执行报告']['废气']
            key['全厂合计'] = dict(zip(exe_gas['污染物'], exe_gas['年度合计']))
        if not exe_water.empty:
            key = result['执行报告']['废水']
            key['全厂排放'] = dict(zip(exe_water['污染物'], exe_water['年度合计']))
        if not licence_gas.empty:
            key = result['许可证']['大气污染物排放']
            key['大气排放总许可量'] = dict(zip(licence_gas['污染物种类'], licence_gas['总许可量']))
        if not licence_water.empty:
            key = result['许可证']['水污染物排放']
            key['全厂排放口总计-年排放量限值'] = dict(zip(licence_water['污染物种类'], licence_water['总许可量']))

        # 异常线索识别
        gas_merge = pd.merge(exe_gas, licence_gas, left_on='污染物', right_on='污染物种类', how='inner')
        gas_merge.loc[:, ['年度合计', '总许可量']] = gas_merge[['年度合计', '总许可量']].apply(pd.to_numeric, errors='coerce')
        exception = gas_merge[gas_merge['年度合计'] > gas_merge['总许可量']]
        if not exception.empty:
            for _, row in exception.iterrows():
                result['异常'].append({
                    '许可证': ['大气污染物排放', '大气排放总许可量', row['污染物种类']],
                    '执行报告': ['废气', '全厂合计', row['污染物']],
                    '规则': '污染物年排放量超过排污许可证年排放量许可限值'
                })

        water_merge = pd.merge(exe_water, licence_water, left_on='污染物', right_on='污染物种类', how='inner')
        water_merge.loc[:, ['年度合计', '总许可量']] = water_merge[['年度合计', '总许可量']].apply(pd.to_numeric, errors='coerce')
        exception = water_merge[water_merge['年度合计'] > water_merge['总许可量']]
        if not exception.empty:
            for _, row in exception.iterrows():
                result['异常'].append({
                    '许可证': ['水污染物排放', '全厂排放口总计-年排放量限值', row['污染物种类']],
                    '执行报告': ['废水', '全厂排放', row['污染物']],
                    '规则': '污染物年排放量超过排污许可证年排放量许可限值'
                })

        return result


if __name__ == '__main__':
    file1_path = '/home/<USER>/project/large_model/材料/debug-许可证'
    file1_type = '许可证'
    file1_name = '91500107MA5U4QJ58P_重庆臻宝科技股份有限公司.docx'
    file2_path = '/home/<USER>/project/large_model/材料/debug-环评'
    file2_type = '环评'
    file2_name = 'flatten-彩云湖污水处理厂提标改造工程.docx'
    result = ent_link_pipeline(file1_path, file1_type, file1_name, file2_path, file2_type, file2_name)
    print(result)

    # file1_path = '/home/<USER>/project/large_model/材料/extract-许可证/data'
    # file1_type = '许可证'
    # file1_name = '915001032028383541_重庆市排水有限公司西彭污水处理厂.docx'
    # file2_path = '/home/<USER>/project/large_model/材料/extract-执行报告/重庆西彭污水处理有限公司'
    # file2_type = '执行报告'
    # file2_name = '执行报告-重庆西彭污水处理有限公司-2022年.docx'
    # result = Exe_Link_Pipeline(file1_path, file1_type, file1_name, file2_path, file2_type, file2_name).run()
    # print(result)