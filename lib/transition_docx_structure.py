# -*- coding: utf-8 -*-
"""
@File  : transition_docx_structure.py
@Author: <PERSON>
@Date  : 2025/2/11 15:21
@Desc  : 
"""
import os
from docx import Document
from docx.oxml import parse_xml
from docx.table import Table
from docx.oxml.ns import qn
from lxml import etree
import pypandoc
import warnings


def cell_is_embedded(cell):
    return cell.tables


def table_is_embedded(table):
    for row in table.rows:
        for cell in row.cells:
            if cell_is_embedded(cell):
                return True
    return False


# 判断文档是报告书还是报告表
def doc_is_table(doc):
    for tbl1 in doc.tables:
        if table_is_embedded(tbl1):
            return True
    return False


def transition_docx_structure(doc, save_path):
    """
    将环评报告表的内嵌表格平铺成新的报告
    """
    if not doc_is_table(doc):
        return

    is_key = lambda x: x % 2 == 0

    def same_as_last_one(idx, row_cells):
        if idx == 0:
            return False
        last = row_cells[idx - 1]._element.xml
        curr = row_cells[idx]._element.xml
        return last == curr

    def process_txt(child):
        """ 处理单元格内容内不必要换行的问题 """
        for t in child.findall('.//' + qn('w:t')):
            t.text = t.text.replace(' ', '')

    # 重组报告表
    new_doc = Document()
    new_doc_element = new_doc.element.body
    for element in doc.element.body:
        if element.tag.endswith('p'):
            new_para_xml = parse_xml(element.xml)

            # 插入新文档中
            new_doc_element.append(new_para_xml)
        elif element.tag.endswith('tbl'):
            table = Table(element, doc.element.body)
            if not table_is_embedded(table):
                table = parse_xml(table._element.xml)
                new_doc.element.body.append(table)
                continue

            lst = []
            save = True
            # 遍历表格中的每一行
            for row in table.rows:
                # 遍历每一行中的每一个单元格
                idx = -1
                raw_cells = row.cells
                for i in range(len(raw_cells)):
                    if same_as_last_one(i, raw_cells):
                        continue
                    idx += 1
                    cell = raw_cells[i]
                    if not cell_is_embedded(cell) and is_key(idx):
                        cell.text = cell.text.replace('\n', '')
                    if save:
                        if not cell_is_embedded(cell):
                            if not is_key(idx):
                                lst.append([raw_cells[i-1], cell])
                        else:
                            save = False
                            # 保存截断前面的表格
                            if lst:
                                new_table = new_doc.add_table(rows=len(lst), cols=2)
                                for i1 in range(len(lst)):
                                    for j1 in range(2):
                                        target_cell = new_table.cell(i1, j1)
                                        for child in lst[i1][j1]._element:
                                            process_txt(child)
                                            source_xml = parse_xml(child.xml)
                                            target_cell._tc.append(source_xml)
                                table_xml = new_table._element
                                new_doc.tables.pop()
                                new_doc_element.append(table_xml)

                            # 保存平铺的第一个嵌套表格
                            if not is_key(idx):
                                for child in raw_cells[i - 1]._element:
                                    process_txt(child)
                                    new_doc_element.append(parse_xml(child.xml))
                                for child in cell._element:
                                    process_txt(child)
                                    if isinstance(child, etree._Element):
                                        child = etree.tostring(child, encoding="unicode")
                                        new_doc_element.append(parse_xml(child))
                                    else:
                                        # print(type(child))
                                        # print(child.text)
                                        new_doc_element.append(parse_xml(child.xml))
                        continue

                    for child in cell._element:
                        process_txt(child)
                        if isinstance(child, etree._Element):
                            child = etree.tostring(child, encoding="unicode")
                            new_doc_element.append(parse_xml(child))
                        else:
                            # print(type(child))
                            # print(child.text)
                            new_doc_element.append(parse_xml(child.xml))
                    for i in range(2):
                        para = new_doc.add_paragraph()

    new_doc.save(save_path)


def convert_docx_type(docx_file_path, txt_file_path):
    output = pypandoc.convert_file(docx_file_path, 'markdown_strict', outputfile=txt_file_path)   # markdown_strict plain
    assert output == ""
    print(f"文件已转换: {txt_file_path}")


def load_docx(path):
    doc = Document(path)
    return doc


def run(directory):
    folders = sorted([i for i in os.listdir(directory) if os.path.isdir(os.path.join(directory,i))])
    for folder in folders:
        path = os.path.join(directory, folder)
        files = [i for i in os.listdir(path)
                 if i.endswith('.docx') and not i.startswith('flatten-') and not i.startswith('~$')]
        if len(files) == 1:
            source_file = os.path.join(path, files[0])
            target_file = os.path.join(path, 'flatten-' + files[0])
            doc = load_docx(source_file)
            transition_docx_structure(doc, target_file)
            if os.path.exists(target_file):
                md_file = target_file.replace('.docx', '.md')
                convert_docx_type(target_file, md_file)
        else:
            warnings.warn(f"文件夹 {folder} 含文件{files}")


if __name__ == '__main__':
    base_path = '/home/<USER>/project/large_model/srv-pollution-jiulongpo-emission/材料/2022年环评项目电子档案资料'
    # path = os.path.join(base_path, '渝（九）环准〔2022〕057号重庆昆嘉机械制造有限公司摩托车轮辋及农机箱体生产项目（西彭）/摩托车轮辋及农机箱体生产项目环评表（报批稿）.docx')
    # path = os.path.join(base_path, '渝（九）环准〔2022〕001号新韵家具家具板材加工项目（壹本科工）/新韵家具环评文本(送审版）.docx')
    # doc = load_docx(path)
    # transition_docx_structure(doc, 'faltten.docx')
    # convert_docx_type('new.docx', 'new.md')
    run(base_path)
