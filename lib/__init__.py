# -*- coding: utf-8 -*-
"""
@File  : __init__.py.py
@Author: <PERSON>
@Date  : 2025/2/25 9:39
@Desc  :
"""
import os
import argparse
import pandas as pd
import json
import numpy as np
from decimal import Decimal

from mod import proj_dir, load_conf_file, load_log_conf
from mod import load_yaml

_log_conf = load_conf_file(f'{proj_dir}/config/log_conf.yml')
logger = load_log_conf(_log_conf)

FREQ_REQUIRED = load_yaml(os.path.join(proj_dir, 'config/freq_required.yml'))
TABLE_RULES = load_yaml(f'{proj_dir}/config/table_rules.yml')
TBL_NAME_MAP = load_yaml(f'{proj_dir}/config/tbl_name_map.yml')
_ENV_CONF = load_yaml(f'{proj_dir}/config/project.yml')
ALL_ENTS = load_yaml(f'{proj_dir}/config/ents.yml')


class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        # elif isinstance(obj, torch.Tensor):
        #     return obj.tolist()
        else:
            return super(MyEncoder, self).default(obj)


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')


def load_default_args():
    parser = argparse.ArgumentParser(description = '配置模式')
    parser.add_argument('-m', '--mode', type = str, help = 'local/test/formal',
                        default = _ENV_CONF['MODE'])
    parser.add_argument('--post', type = str2bool, help = '是否使用清洗后的数据库', default=_ENV_CONF["POST"])
    args = parser.parse_args()
    return args


args = load_default_args()
mode = args.mode
if mode == 'local':
    logger.info('当前运行模式为：本地模式')
elif mode == 'test':
    logger.info('当前运行模式为：测试模式')
elif mode == 'formal':
    logger.info('当前运行模式为：正式模式')
else:
    logger.error('请输入正确的运行模式')
    exit()
for k, v in _ENV_CONF[mode].items():
    _ENV_CONF[k] = v
print(_ENV_CONF)

post = args.post

ori_database_conf = {
    'host': _ENV_CONF['ORI_DB']['DB_HOST'],
    'port': _ENV_CONF['ORI_DB']['DB_PORT'],
    'database': _ENV_CONF['ORI_DB']['DB_NAME'],
    'user': _ENV_CONF['ORI_DB']['DB_USER'],
    'password': _ENV_CONF['ORI_DB']['DB_PASSWD']
}

main_database_conf = {
    'host': _ENV_CONF['MAIN_DB']['DB_HOST'],
    'port': _ENV_CONF['MAIN_DB']['DB_PORT'],
    'database': _ENV_CONF['MAIN_DB']['DB_NAME'],
    'user': _ENV_CONF['MAIN_DB']['DB_USER'],
    'password': _ENV_CONF['MAIN_DB']['DB_PASSWD']
}

# llm_conf = {'LLM_URL': args.llm_url, 'LLM_MODEL': args.llm_model, 'LLM_AUTH_TOKEN': args.llm_auth_token,
#             'STOP_TOKEN_IDS': args.stop_token_ids, 'RANDOM_SEED': args.random_seed}

license_number = pd.read_excel(f'{proj_dir}/file/anomaly_report/basic_information_enterprise_chongqing.xlsx',
                               header=0, dtype={'pol_permit_number': str})
license_number: dict = license_number[['enterprise_name', 'pol_permit_number']].set_index('enterprise_name').to_dict()['pol_permit_number']
DOCX_UPLOA_URL = _ENV_CONF['DOCX_UPLOA_URL']


class Global:
    GET_DATA = {}
    GET_EXE_DATA = {}
    GET_OIL_DATA = {}

    @classmethod
    def clean(cls):
        cls.GET_DATA = {}
        cls.GET_EXE_DATA = {}
        cls.GET_OIL_DATA = {}
