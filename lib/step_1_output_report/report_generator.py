#!/usr/bin/env python
#-*-coding: utf-8 -*-

"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-18 5:42:11 PM

  @ FilePath:  -> srv-jiulongpo-word-check -> lib -> check -> step_1_output_report.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-
import re
import sys
from collections import deque, defaultdict

from typing import Union
from docx import Document
from docx.table import Table
from docx.shared import Pt
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT

sys.path.append('../../')

from lib import  logger

def is_valid(s):
    return not bool(re.fullmatch(r'^[A-Za-z0-9_]*$', s))


class ReportGenerator:
    def __init__(self,  output_path: str) -> None:
        """
        初始化ReportGenerator对象
        @param output_path: 输出文件路径
        """
        self.output_path = output_path
        self.doc = Document()        

    def get_table_name(self, table: Table = None) -> Union[str, list]:
        """获取表格名称"""
        if table is not None:
            return table._element.getprevious().text
        table_name = []
        for table in self.doc.tables:
            # 假设标题在表格前面的一段文字中, 将文字和文字所在章节的表头拼接起来作为表名
            if table._element.getprevious() is not None:
                table_name.append(table._element.getprevious().text)
        return table_name

    def find_table_by_name(self, table_indicator: str) -> Union[Table, None]:
        """根据表格名称查找表格"""
        for table in self.doc.tables:
            if table_indicator in self.get_table_name(table):
                return table
        else:
            logger.error("表格不存在")
            return None

    def add_row(self, table: Table, row_data: list, style=None, format=False) -> None:

        """向表格添加行并设置文本，使用仿宋五号字体，水平和垂直居中显示"""
        row = table.add_row().cells
        for i, text in enumerate(row_data):
            row[i].text = str(text)
        if format:
            self.format_table()

    @staticmethod
    def format_table(table: Table):
        for row in table.rows:  # 先遍历行
            for cell in row.cells:  # 再遍历单元格        
                # 设置段落水平居中
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                    
                    # 设置字体为仿宋五号
                    for run in paragraph.runs:
                        run.font.name = '仿宋'
                        run.font.name = 'Times New Roman'
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋')
                        run.font.size = Pt(10.5)  # 五号字体大小为10.5磅
                    
                # 设置单元格垂直居中
                cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            table.autofit = True

    @staticmethod
    def set_table_header_bg_color(table: Table, color=None) -> None:
        """设置表头背景颜色, 灰色: D9D9D9"""
        if color is None:
            return
        
        tbl = table._tbl
        for row_idx, row in enumerate(tbl.tr_lst):
            if row_idx == 0:  # 仅处理表头行
                for cell in row.tc_lst:
                    tcPr = cell.get_or_add_tcPr()
                    shade = OxmlElement('w:shd')
                    shade.set(qn('w:fill'), color)
                    tcPr.append(shade)

    def replace_text_keep_format(self, old_text, new_text):
        """
        替换段落中的文本，同时保留格式
        """
        for paragraph in self.doc.paragraphs:
            if old_text in paragraph.text:
                # 遍历段落的每个 run
                for run in paragraph.runs:
                    if old_text in run.text:
                        # 替换 run 中的文本
                        run.text = run.text.replace(old_text, new_text)

    def get_number(self, level, style='一', increase=True):
        
        self.number: dict = {1: [0, '零'], 2: [0, '0)']} if 'number' not in self.__dict__ else self.number
        num = self.number[level][0] + 1 if increase else self.number[level][0]

        if style != '一':
            if level in self.number.keys():
                self.number[level][0] = num
                self.number[level][1] = f'{num})'
            else:
                self.number = {**self.number, level: [num, f'{num})']}
            return
        
        chinese_digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
        units = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '兆']
        if num == 0:
            return chinese_digits[0]
        digits = []
        temp = num
        while temp > 0:
            digits.append(temp % 10)
            temp //= 10
        digits = digits[::-1]
        length = len(digits)
        result = []
        zero_flag = False
        for i in range(length):
            d = digits[i]
            unit_idx = length - i - 1
            unit = units[unit_idx] if unit_idx < len(units) else ''
            if d == 0:
                if not zero_flag and (unit_idx % 4 != 0 or unit_idx == 0):
                    result.append(chinese_digits[d])
                    zero_flag = True
            else:
                result.append(chinese_digits[d])
                if unit:
                    result.append(unit)
                zero_flag = False
        chinese_str = ''.join(result)
        replacements = [
            ('零十', '零'), ('零百', '零'), ('零千', '零'),
            ('零万', '万'), ('零亿', '亿'), ('零兆', '兆'),
            ('零零', '零')]
        for old, new in replacements:
            chinese_str = chinese_str.replace(old, new)
        if chinese_str.startswith('零'):
            chinese_str = chinese_str[1:]
        if chinese_str.endswith('零'):
            chinese_str = chinese_str[:-1]
        if chinese_str.startswith('一十'):
            chinese_str = chinese_str[1:]
        
        if level in self.number.keys():
            self.number[level][0] = num
            self.number[level][1] = chinese_str
        else:
            self.number = {**self.number, level: [num, chinese_str]}

    def add_specific_table(self, row_number, headers, merged_header: dict=None, format=False):
        """添加子问题表格（支持合并表头）"""

        table = self.doc.add_table(rows=row_number, cols=len(headers)) 
        table.style = "Table Grid"
        
        def merge_cells(cell1, cell2):
            """合并两个单元格"""
            cell1.merge(cell2)

        # 合并表头单元格（如果需要）
        if merged_header:
            cell = table.cell(0, merged_header["cols"][0])
            cell.text = merged_header["text"]
            for col in merged_header["cols"][1:]:
                merge_cells(table.cell(0, merged_header["cols"][0]), table.cell(0, col))
            for _ in range(len(headers)):
                if _ not in merged_header['cols']:
                    merge_cells(table.cell(0, _), table.cell(1, _))

        # 填充子标题
        sub_hdr = table.rows[-1].cells
        for i, header in enumerate(headers):
            sub_hdr[i].text = header

        if format:
            self.format_table(table)

        return table

    def write_doc(self):
        self.doc.save(self.output_path)

    def create_table_with_merges(self, data):
        """
        根据给定的一个2维list，创建一个表格，将表格中填入<up>的单元格与临近的上单元格合并，保留上单元格的值，
        <left>与左单元格合并，<right>与右单元格合并，<down>与下单元格合并，直到合并完所有的标志，输入的合并区域是规范的长方形。
        """
        # 创建文档和表格
        rows = len(data)
        cols = len(data[0]) if rows > 0 else 0
        table = self.doc.add_table(rows=rows, cols=cols)

        # 设置表格样式
        table.style = 'Table Grid'

        # 方向映射
        directions = {
            '<up>': (-1, 0),
            '<down>': (1, 0),
            '<left>': (0, -1),
            '<right>': (0, 1)
        }

        # 存储主单元格和其标记单元格的映射
        master_dict = defaultdict(list)
        # 存储每个单元格的主单元格坐标
        master_map = {}
        # 存储每个单元格的最终值（非标记值）
        values = {}

        # 初始化值和主单元格映射
        for i in range(rows):
            for j in range(cols):
                cell_value = data[i][j]
                if cell_value in directions:
                    values[(i, j)] = ""  # 标记单元格初始化为空
                    master_map[(i, j)] = None
                else:
                    values[(i, j)] = cell_value
                    master_map[(i, j)] = (i, j)  # 非标记单元格的主单元格是自己
                    master_dict[(i, j)].append((i, j))  # 添加到自己的主单元格列表

        # 查找标记单元格的主单元格
        for i in range(rows):
            for j in range(cols):
                if data[i][j] in directions:
                    x, y = i, j
                    # 沿着方向查找主单元格（非标记单元格）
                    while 0 <= x < rows and 0 <= y < cols and data[x][y] in directions:
                        dx, dy = directions[data[x][y]]
                        x += dx
                        y += dy

                    # 如果找到有效主单元格
                    if 0 <= x < rows and 0 <= y < cols:
                        master_map[(i, j)] = (x, y)
                        master_dict[(x, y)].append((i, j))

        # 填充表格（只填非标记值）
        for i in range(rows):
            for j in range(cols):
                if data[i][j] not in directions:
                    table.cell(i, j).text = values[(i, j)]

        # 合并单元格
        processed_regions = set()
        for master, cells in master_dict.items():
            if master not in processed_regions:
                # 收集所有属于这个主单元格的区域
                all_cells = set(cells)
                all_cells.add(master)

                # 计算合并区域的边界
                min_row = min(i for i, j in all_cells)
                max_row = max(i for i, j in all_cells)
                min_col = min(j for i, j in all_cells)
                max_col = max(j for i, j in all_cells)

                # 确保区域是连续的矩形
                for r in range(min_row, max_row + 1):
                    for c in range(min_col, max_col + 1):
                        if (r, c) not in all_cells:
                            # 如果发现矩形中有不属于该区域的单元格，跳过合并
                            break
                    else:
                        continue
                    break
                else:
                    # 只合并矩形区域（至少2个单元格）
                    if len(all_cells) > 1:
                        try:
                            # 执行合并
                            table.cell(min_row, min_col).merge(
                                table.cell(max_row, max_col)
                            )
                            # 标记该区域已处理
                            for cell in all_cells:
                                processed_regions.add(cell)
                        except Exception as e:
                            print(f"合并失败: {min_row},{min_col} 到 {max_row},{max_col} - {str(e)}")

        self.format_table(table)
        return table

        