"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2025-03-18 5:42:11 PM


  @ FilePath:  -> srv-jiulongpo-word-check -> lib -> step_1_output_report -> step_1_output_bed.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-
import os
import re
import sys
import json
import requests
import pandas as pd

from typing import Union
from thefuzz import process
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor, ALL_COMPLETED, wait

sys.path.append('../../')

from lib import  logger, license_number, proj_dir, DOCX_UPLOA_URL
from lib.step_1_output_report.report_generator import ReportGenerator
from lib.check.table_name_generator import get_table_name
from lib.check.base import get_db_cursor


def is_valid(s):
    return not bool(re.fullmatch(r'^[A-Za-z0-9_]*$', s))


class MyReportGenerator(ReportGenerator):
    
    # 初始化
    def __init__(self, output_path, data, company_id, company_name, uuid_id_map, id_uuid_map) -> None:
        """
        初始化ReportGenerator对象
        @param output_path: 输出文件路径
        @param json_info: json数据
        @param company: 公司名称
        @param licence_number: 排污许可证编号
        """
        super().__init__(output_path)

        self.data = data
        self.company_id = company_id
        self.company = company_name
        self.uuid_id_map = uuid_id_map
        self.id_uuid_map = id_uuid_map
        
    def add_formatted_title(self, title_text: SyntaxError, font_size=16, bold=False, align='LEFT', p_before=0, p_after=0, style=None):
        """
        添加格式化标题到文档
        :param doc: Word文档对象
        :param title_text: 标题文本（支持中英混合）
        :param font_size: 字号（磅）16:文字号三号对应16磅
        """

        # 创建居中对齐的段落
        paragraph = self.doc.add_paragraph(text=title_text)

        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.__dict__.get(align)
        paragraph.paragraph_format.space_before = Pt(p_before)
        paragraph.paragraph_format.space_after = Pt(p_after)
        paragraph.paragraph_format.line_spacing = 1.5
        if style:
            paragraph.style = style

        for run in paragraph.runs:
            run.font.name = '仿宋'
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋')
            # 统一设置字号和加粗
            run.font.size = Pt(font_size)           # 中
            run.font.bold = bold
            run.font.color.rgb = RGBColor(0, 0, 0)
            run.font.underline = False

    def generate(self):
        
        self.add_formatted_title(f"{self.company}", 16, True, 'CENTER')
        self.add_formatted_title("许可证-一般环境问题", 14, True, 'RIGHT')

        # 4-1单份环评报告中记载的污染物排放口未在许可证中全部体现
        self.generate_question_4_1()

        # 4-2许可证记载的污染物排气筒高度/排气筒内径/经纬度与环评不匹配
        self.generate_question_4_2()

        # 医疗机构病床数与卫健委数据不符
        self.generate_question_1()

        # oil
        self.generate_question_quantity()
        self.generate_question_volume()

        for table in self.doc.tables:
            self.format_table(table)
        self.write_doc()

    def generate_question_4_1(self):
        """ 单份环评报告中记载的污染物排放口未在许可证中全部体现 """
        def create_table():
            return self.add_specific_table(1, headers=["企业名称", "监测类别", "排口名称"])

        if 'json_port_base' not in self.data.keys():
            return False
        json_port_base = self.data['json_port_base']

        if 'uuid_hp_map' not in self.uuid_id_map:
            return False
        uuid_hp_map = self.uuid_id_map['uuid_hp_map']

        if self.company_id not in uuid_hp_map:
            return False
        hp_id = uuid_hp_map[self.company_id]

        abnormal = [item for item in json_port_base if str(item['hp_enterprise_id']) == hp_id]
        if not abnormal:
            return False
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-04-1单份环评报告中记载的污染物排放口未在许可证中全部体现', 14, True, style='Heading 1')

        table = create_table()
        for item in abnormal:
            self.add_row(
                table, [item['hp_enterprise_name'], item['monitoring_type'], item['hp_outlet_name']], 'Normal'
            )
        self.add_formatted_title('', 12)


    def generate_question_4_2(self):
        """ 许可证记载的污染物排气筒高度/排气筒内径/经纬度与环评不匹配 """
        if 'json_gas_port' not in self.data.keys():
            return False
        json_gas_port = self.data['json_gas_port']

        if 'uuid_f_map' not in self.uuid_id_map:
            return False
        uuid_f_map = self.uuid_id_map['uuid_f_map']

        if self.company_id not in uuid_f_map:
            return False
        f_id = uuid_f_map[self.company_id]

        abnormal = [item for item in json_gas_port if str(item['f_enterprise_id']) == f_id]
        if not abnormal:
            return False

        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-04-2许可证记载的污染物排气筒高度/排气筒内径/经纬度与环评不匹配', 14, True, style='Heading 1')
        title = [
            ['企业名称', '监测类别', '排口名称', '排气筒高度', '<left>', '排气筒内径', '<left>', '排气筒经度', '<left>', '排气筒纬度', '<left>'],
            ['<up>', '<up>', '<up>', '许可证', '环评', '许可证', '环评', '许可证', '环评', '许可证', '环评']
        ]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            company = item['f_enterprise_name']
            monitoring_type = item['monitoring_type']
            outlet_name = item['pw_outlet_name'],
            pw_height = item['permit_exhaust_height']
            hp_height = item['eia_exhaust_height']
            pw_diameter = item['permit_exhaust_diameter']
            hp_diameter = item['eia_exhaust_diameter']
            pw_lon = item['permit_exhaust_lon']
            hp_lon = item['eia_exhaust_lon']
            pw_lat = item['permit_exhaust_lat']
            hp_lat = item['eia_exhaust_lat']
            self.add_row(
                table,
                [
                    company, monitoring_type, outlet_name, pw_height, hp_height, pw_diameter, hp_diameter, pw_lon, hp_lon, pw_lat, hp_lat
                ]
            )
        self.add_formatted_title('', 12)
        return True


    def generate_question_quantity(self):
        if 'json_oil_quantity' not in self.data.keys():
            return False
        oil_quantity = self.data['json_oil_quantity']

        if 'uuid_f_map' not in self.uuid_id_map:
            return False
        uuid_f_map = self.uuid_id_map['uuid_f_map']

        if self.company_id not in uuid_f_map:
            return False
        f_id = uuid_f_map[self.company_id]

        if f_id not in oil_quantity:
            return False

        oil_quantity = oil_quantity[f_id]
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-06-1-加油站油罐个数与环境影响评价报告不符', 14, True, style='Heading 1')
        title = [
            ['企业名称', '生产设施名称', '许可证数量', '环评数量', '环评报告记载且许可证未记载储罐类型']
        ]
        table = self.create_table_with_merges(title)
        company = oil_quantity['enterprise_name']
        if 'quantity_mismatch' in oil_quantity:
            for row in oil_quantity['quantity_mismatch']:
                self.add_row(
                    table,
                    ([company, row['equip_name'], row['permit_count'], row['eia_count'], '']),
                    'Normal'
                )
        if 'only_eia' in oil_quantity and oil_quantity['only_eia']:
            self.add_row(
                table,
                (company, '', '', '', '、'.join(oil_quantity['only_eia'])),
                'Normal'
            )
        self.add_formatted_title('', 12)


    def generate_question_volume(self):
        def create_table():
            return self.add_specific_table(1, headers=['企业名称', '生产设施名称', '公秤容积_许可证', '公称容积_环评'])

        if 'json_oil_volume' not in self.data.keys():
            return False
        oil_volume = self.data['json_oil_volume']

        if 'uuid_f_map' not in self.uuid_id_map:
            return False
        uuid_f_map = self.uuid_id_map['uuid_f_map']

        if self.company_id not in uuid_f_map:
            return False
        f_id = uuid_f_map[self.company_id]

        if not oil_volume:
            return
        abnormal = oil_volume[str(oil_volume['enterprise_id']) == f_id]
        if not abnormal:
            return

        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-06-2-加油站油罐容积与环境影响评价报告不符', 14, True,
                                 style='Heading 1')
        table = create_table()
        for _, row in abnormal.iterrows():
            self.add_row(
                table,
                ([row['enterprise_name'], row['equip_name'], row['permit_volume'], row['eia_volume']]),
                'Normal'
            )
        self.add_formatted_title('', 12)


    def generate_question_1(self):
        """ 医疗机构病床数与卫健委数据不符 """
        if 'json_bed' not in self.data.keys():
            return False
        json_bed = self.data['json_bed']

        if 'uuid_f_map' not in self.uuid_id_map:
            return False
        uuid_f_map = self.uuid_id_map['uuid_f_map']

        if self.company_id not in uuid_f_map:
            return False
        f_id = uuid_f_map[self.company_id]

        abnormal = [item for item in json_bed if str(item['f_enterprise_id']) == f_id]
        if not abnormal:
            return False
        self.get_number(1, style='一', increase=True)
        self.add_formatted_title(f'{self.number[1][1]}、XKZ-01-1医疗机构排污许可证登记的病床量与卫健委提供的医疗机构编制床位数不符', 14, True, style='Heading 1')
        title = [['企业名称', '卫健委床位数', '许可证床位数']]
        table = self.create_table_with_merges(title)
        for item in abnormal:
            self.add_row(
                table,
                [self.company, item['hospital_health_bed_num'], item['permit_bed_num']],
                'Normal'
            )
        self.add_formatted_title('', 12)


def get_ents_licence_number(company_name: str) -> str:

    if license_number.get(company_name, '') == '':
        return _fuzzy_match(company_name, license_number)
    else:
        return license_number[company_name], company_name


def _fuzzy_match(name, dict_name, threshold=85):
    """模糊匹配企业名称"""

    match = process.extractOne(name, list(dict_name.keys()))
    if match[1] >= threshold:
        # 返回license_number, 企业标准名称name
        return dict_name[match[0]], match[0]
    else:
        logger.warning(f'模糊匹配企业名称失败: {name}')
        return '', name


def create_permit_instance(fp, data, company_id, company_name, uuid_id_map, id_uuid_map):
    """创建类实例，并生成docx报告"""
    report = MyReportGenerator(fp, data, company_id, company_name, uuid_id_map, id_uuid_map)
    report.generate()


def preprocess_json(city, data):
    """ 统一许可证企业ID-name """
    abnormal_table = get_table_name("basic_information_enterprise_map", city)
    db = get_db_cursor('main')
    sql = f"""
        select * from {abnormal_table}
        where region_code_city in (
            select city_code from dim_ref_comm_prov_city where city_name = '{city}'
        )
    """
    all_id_map = db.query_sql(sql)
    db.close()

    id_uuid_map = {}
    uuid_id_map = {}

    tmp = all_id_map[all_id_map['f_enterprise_id'].notnull()]
    f_uuid_map = tmp.set_index('f_enterprise_id')['uuid'].to_dict()
    id_uuid_map['f_uuid_map'] = f_uuid_map

    tmp = all_id_map[all_id_map['hp_enterprise_id'].notnull()]
    hp_uuid_map = tmp.set_index('hp_enterprise_id')['uuid'].to_dict()
    id_uuid_map['hp_uuid_map'] = hp_uuid_map

    # 交换kv
    for _type, group in id_uuid_map.items():
        exchange = {v: k for k, v in group.items()}
        if _type == 'f_uuid_map':
            uuid_id_map['uuid_f_map'] = exchange
        elif _type == 'hp_uuid_map':
            uuid_id_map['uuid_hp_map'] = exchange

    id_name = {}

    for _type, group in data.items():
        if _type == 'json_oil_quantity':
            for company_id, company_info in group.items():
                if str(company_id) in f_uuid_map:
                    id_name[f_uuid_map[str(company_id)]] = company_info['enterprise_name']
                else:
                    logger.warning(f'json_oil_quantity中企业id {company_id}, {company_info["enterprise_name"]} 未找到对应的uuid')
        elif _type == 'json_oil_volume':
            for row in group:
                if str(row['enterprise_id']) in f_uuid_map:
                    id_name[f_uuid_map[str(row['enterprise_id'])]] = row['enterprise_name']
                else:
                    logger.warning(f'json_oil_volume中企业id {row["enterprise_id"]}, {row["enterprise_name"]} 未找到对应的uuid')
        elif _type == 'json_bed':
            for item in group:
                if str(item['f_enterprise_id']) in f_uuid_map:
                    id_name[f_uuid_map[str(item['f_enterprise_id'])]] = item['permit_unit_name']
                else:
                    logger.warning(f'json_bed中企业id {item["f_enterprise_id"]}, {item["permit_unit_name"]} 未找到对应的uuid')
        elif _type == 'json_port_base':
            for item in group:
                if str(item['hp_enterprise_id']) in hp_uuid_map:
                    id_name[hp_uuid_map[str(item['hp_enterprise_id'])]] = item['hp_enterprise_name']
                else:
                    logger.warning(f'json_port_base中企业hp-id {item["hp_enterprise_id"]}, {item["hp_enterprise_name"]} 未找到对应的uuid')
        elif _type == 'json_gas_port':
            for item in group:
                if str(item['f_enterprise_id']) in f_uuid_map:
                    id_name[f_uuid_map[str(item['f_enterprise_id'])]] = item['f_enterprise_name']
                else:
                    logger.warning(f'json_gas_port中企业id {item["f_enterprise_id"]}, {item["f_enterprise_name"]} 未找到对应的uuid')

    return id_name, uuid_id_map, id_uuid_map


def batch_generate_reports_permit(city, data, job_id, use_mp = False, rerun=False):
    """批量生产docx"""
    files_path = []
    file_dir = f'{proj_dir}/data/jobs/{job_id}'
    os.makedirs(file_dir, exist_ok=True)

    id_name, uuid_id_map, id_uuid_map = preprocess_json(city, data)
    name_id = {v: k for k, v in id_name.items()}

    if use_mp:
        with Pool(processes = min(8, os.cpu_count())) as pool:
            for company_id, company_name in id_name.items():
                # if not is_valid(company_name):
                #     continue
                # _, name_ = get_ents_licence_number(company_name)
                fp = f'{proj_dir}/data/jobs/{job_id}/XKJ许可证-一般环境问题-{company_name}.docx'
                if os.path.isfile(fp):
                    files_path.append([fp, company_name, company_id])
                else:
                    pool.apply_async(create_permit_instance,
                                     (fp, data, company_id, company_name, uuid_id_map, id_uuid_map))
                    files_path.append([fp, company_name, company_name])
            pool.close()
            pool.join()
    else:
        for company_id, company_name in id_name.items():
            # if not is_valid(company_name):
            #     continue
            # _, name_ = get_ents_licence_number(company_name)
            fp = f'{proj_dir}/data/jobs/{job_id}/XKJ许可证-一般环境问题-{company_name}.docx'
            if os.path.isfile(fp):
                files_path.append([fp, company_name, company_id])
            else:
                create_permit_instance(fp, data, company_id, company_name, uuid_id_map, id_uuid_map)
                files_path.append([fp, company_name, company_id])
    return files_path


def upload_file(fp, company_name, company_id):
    """上传文件"""
    url = f'{DOCX_UPLOA_URL}'
    if url:
        assert os.path.isfile(fp), f'to uploaed file not existd -> {fp}'
        with open(fp, 'rb') as f1:
            file_name = os.path.basename(fp)
            files = [('file', (file_name, f1, 'application/wps-office.docx'))]
            response = requests.post(url, files = files)
        assert response.status_code < 400, f'upload file error, status code -> {response.status_code}'
        info = response.json()['data']
    else:
        info = {
            "ossId": "",
            "url": url,
            "fileName": os.path.basename(fp),
        }
    info['ent_name'] = company_name
    info['uuid'] = company_id
    return info


def batch_or_single_upload_permit(files_path, use_mt = True):
    """使用多线程或者循环上传文件"""
    # logger.info('==> uploading docx files...')
    if use_mt:
        with ThreadPoolExecutor(max_workers = 16) as t:
            all_task = [t.submit(upload_file, fp, company_name, company_id)
                        for fp, company_name, company_id in files_path]
            wait(all_task, return_when = ALL_COMPLETED)
        info_list = [task.result() for task in all_task]
    else:
        info_list = []
        for fp, company_name, company_id in files_path:
            info = upload_file(fp, company_name, company_id)
            info_list.append(info)
    return info_list


if __name__ == "__main__":
    # 读取JSON数据
    json_path = f'{proj_dir}/data/jobs/start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0/result_bed.json'
    with open(json_path, 'r', encoding='utf-8') as f:
        data = {'data': json.load(f)}

    # company_names = [_['单位名称'] for _ in data['data']]
    # for name in company_names:
    #     lic_, name_ = _fuzzy_match(name, license_number)
    #     os.makedirs(f'{proj_dir}/data/bed/', exist_ok=True)
    #     report = MyReportGenerator(f'{proj_dir}/data/{name_}_bed.docx', data, name, license_number)
    #     report.generate()

    print(batch_or_single_upload_permit(batch_generate_reports_permit(data,
        'start_time-2021-01-01_end_time-2023-02-25_time-2021,2022_0', False), False))