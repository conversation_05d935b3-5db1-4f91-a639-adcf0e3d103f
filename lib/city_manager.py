# -*- coding: utf-8 -*-
"""
@File  : city_manager.py
@Author: <PERSON>
@Date  : 2025/8/21
@Desc  : 城市数据管理模块，提供基于数据库的城市校验功能
"""
import threading
import time
from datetime import datetime, timedelta
from typing import List, Optional
from lib import logger


class CityManager:
    """城市数据管理器，提供智能缓存机制"""

    def __init__(self, cache_hours: int = 24):
        """
        初始化城市管理器

        Args:
            cache_hours: 缓存有效期（小时），默认24小时
        """
        self._cache: List[str] = []
        self._last_update: Optional[datetime] = None
        self._cache_hours = cache_hours
        self._lock = threading.RLock()  # 线程安全锁
        self._loading = False  # 防止重复加载
        self._cache_hits = 0  # 缓存命中次数
        self._cache_misses = 0  # 缓存未命中次数

        # 启动时加载城市数据
        self._load_cities_from_db()
    
    def get_supported_cities(self) -> List[str]:
        """
        获取支持的城市列表
        
        Returns:
            城市名称列表
        """
        with self._lock:
            # 检查缓存是否过期
            if self._is_cache_expired():
                logger.info("城市缓存已过期，正在刷新...")
                self._load_cities_from_db()
            
            return self._cache.copy()
    
    def is_valid_city(self, city: str) -> bool:
        """
        检查城市是否有效

        Args:
            city: 城市名称

        Returns:
            True if valid, False otherwise
        """
        if not city:
            self._cache_misses += 1
            return False

        supported_cities = self.get_supported_cities()
        is_valid = city in supported_cities

        if is_valid:
            self._cache_hits += 1
        else:
            self._cache_misses += 1

        return is_valid
    

    def get_cache_info(self) -> dict:
        """
        获取缓存信息

        Returns:
            缓存状态信息
        """
        with self._lock:
            total_requests = self._cache_hits + self._cache_misses
            hit_rate = (self._cache_hits / total_requests * 100) if total_requests > 0 else 0

            return {
                "city_count": len(self._cache),
                "last_updated": self._last_update.isoformat() if self._last_update else None,
                "cache_expired": self._is_cache_expired(),
                "cache_hours": self._cache_hours,
                "performance": {
                    "cache_hits": self._cache_hits,
                    "cache_misses": self._cache_misses,
                    "hit_rate_percent": round(hit_rate, 2),
                    "total_requests": total_requests
                }
            }
    
    def _is_cache_expired(self) -> bool:
        """检查缓存是否过期"""
        if not self._last_update:
            return True
        
        expiry_time = self._last_update + timedelta(hours=self._cache_hours)
        return datetime.now() > expiry_time
    
    def _load_cities_from_db(self) -> bool:
        """
        从数据库加载城市数据

        Returns:
            True if successful, False otherwise
        """
        with self._lock:
            # 防止重复加载
            if self._loading:
                logger.info("城市数据正在加载中，跳过重复请求")
                return True

            self._loading = True

        try:
            # 导入数据库连接函数
            from lib.check.base import get_db_cursor

            logger.info("开始从数据库加载城市数据...")
            db = get_db_cursor('main')

            # 查询所有有效的城市名称
            sql = """
                SELECT DISTINCT city_name
                FROM dim_ref_comm_prov_city
                WHERE city_name IS NOT NULL
                  AND city_name != ''
                  AND LENGTH(TRIM(city_name)) > 0
                ORDER BY city_name
            """

            result = db.query_sql(sql)
            db.close()

            if result.empty:
                logger.error("数据库中未找到城市数据")
                with self._lock:
                    self._cache = []
                    self._loading = False
                return False

            # 数据验证和清洗
            cities = result['city_name'].tolist()
            valid_cities = [city.strip() for city in cities if city and city.strip()]

            if not valid_cities:
                logger.error("数据库返回的城市数据无效")
                with self._lock:
                    self._cache = []
                    self._loading = False
                return False

            # 更新缓存
            with self._lock:
                self._cache = valid_cities
                self._last_update = datetime.now()
                self._loading = False

            logger.info(f"成功加载 {len(self._cache)} 个城市数据")
            logger.debug(f"支持的城市: {self._cache[:10]}...")  # 只显示前10个

            return True

        except Exception as e:
            logger.error(f"从数据库加载城市数据失败: {str(e)}")

            with self._lock:
                self._loading = False
                # 如果是首次加载失败，清空缓存
                if not self._cache:
                    logger.error("首次加载失败，无法获取城市数据")
                else:
                    logger.info("加载失败，继续使用旧缓存数据")

            return len(self._cache) > 0
    
# 创建全局实例
city_manager = CityManager()
