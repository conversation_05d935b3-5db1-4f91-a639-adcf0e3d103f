# -*- coding: utf-8 -*-
"""
@File  : valid.py
@Author: <PERSON>
@Date  : 2024/12/12 11:06
@Desc  : 
"""
import os
import traceback

import pandas as pd
from lib import proj_dir
from lib.step_2_extract_pdf_and_llm_rag_0.step_2_llm_analyse import llm_analyse


pd.set_option('display.width', 200, 'display.max_columns', 5, 'display.max_rows', 100)
path = os.path.join(proj_dir, '材料', '九龙坡执法案例信息标注.xlsx')
data = pd.read_excel(path, sheet_name='valid', usecols=['序号', '问询笔录', '违法主体', 'target', '违法行为场景'])
data.dropna(axis=0, subset=['序号', 'target'], how='any', inplace=True)
data['target'] = data['target'].apply(lambda x: [int(i) for i in x.split('\n')] if isinstance(x, str) else [int(x)])
data['pred'] = [[] for _ in range(len(data))]

print(data)

for idx, row in data.iterrows():
    print('-----------------------------------processing ', idx)
    for i in range(3):
        try:
            pred = llm_analyse(row['问询笔录'])
            break
        except Exception:
            print(traceback.format_exc())
    data.at[idx, 'pred'] = [int(i) for i in pred.keys()]

    tmp = data[['pred', 'target']].copy()
    tmp['intersect'] = tmp.apply(lambda x: list(set(x['target']).intersection(set(x['pred']))), axis=1)
    tmp['precision'] = tmp.apply(lambda x: len(x['intersect']) / (len(x['pred'])+1e-6), axis=1)
    tmp['recall'] = tmp.apply(lambda x: len(x['intersect']) / (len(x['target'])+1e-6), axis=1)
    precision = tmp['precision'].mean()
    recall = tmp['recall'].mean()
    print(tmp)
    print(precision, recall)
