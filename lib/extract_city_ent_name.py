# -*- coding: utf-8 -*-
"""
@File  : extract_city_name.py
@Author: <PERSON>
@Date  : 2025/3/25 9:45
@Desc  : 
"""
import os
import pandas as pd

from lib import proj_dir
from mod import load_yaml, save_yaml


base_path = os.path.join(proj_dir, 'licence')

def extract_ent_from_city(city):
    """ 提取指定城市文件夹中所有文件夹中的企业 """
    ent_lst = []
    dir_path = os.path.join(base_path, city)
    file_names = os.listdir(dir_path)
    for file_name in file_names:
        file_path = os.path.join(dir_path, file_name)
        data = pd.read_csv(file_path)
        cols = data['单位名称'].tolist()
        ent_lst.extend(cols)
    ent_lst = list(set(ent_lst))
    return ent_lst


def extract_all_cities():
    """ 提取所有城市的企业 """
    dct = {}
    cities = os.listdir(base_path)
    for city in cities:
        ent_lst = extract_ent_from_city(city)
        dct[city] = ent_lst
    return dct


def update_ents_yml():
    """ 提取所有城市企业并生成ents.yml配置文件，九龙坡企业不进行替换 """
    all_cities = extract_all_cities()
    ents_yml = load_yaml(os.path.join(proj_dir, 'config', 'ents.yml'))
    if '九龙坡区' in ents_yml:
        all_cities['九龙坡区'] = ents_yml['九龙坡区']
    save_yaml(all_cities, os.path.join(proj_dir, 'config', 'ents.yml'))
    return


if __name__ == '__main__':
    # res = extract_all_cities()
    update_ents_yml()
