# -*- coding: utf-8 -*-
"""
@File  : pdf_2_docx.py
@Author: <PERSON>
@Date  : 2025/6/19 9:54
@Desc  : 
"""
import os
from tqdm import tqdm
from pdf2docx import Converter


def pdf_to_docx(pdf_path, docx_path):
    # 创建转换器对象
    cv = Converter(pdf_path)

    # 关键参数设置（保留表格结构）：
    cv.convert(
        docx_path,
        start=0,  # 起始页码
        end=None,  # 结束页码（None表示全部）
        combine_continuous_tables=True,  # 合并跨页表格
        recognize_vertical_text=True,  # 识别垂直文本
        table_analysis=True,  # 启用表格结构分析
        layout_analysis=True,
        table_settings={"vertical_strategy": "text",  # 基于文本位置检测列
                        "horizontal_strategy": "lines", # 优先使用实线检测行
                        "join_tolerance": 1.5,       # 提高断线连接宽容度
                        "min_border_width": 0.1}      # 识别更细的表格线
    )
    cv.close()


def main(base_path):
    folders = [
        os.path.join(base_path, folder)
        for folder in os.listdir(base_path)
        if os.path.isdir(os.path.join(base_path, folder))
    ]
    for folder in folders:
        all_files = [file for file in os.listdir(folder) if not file.startswith('~$')]
        for file in all_files:
            if file.endswith('.pdf'):
                if file.replace('.pdf', '.docx') not in all_files:
                    pdf_to_docx(
                        os.path.join(folder, file),
                        os.path.join(folder, 'convert_'+file.replace('.pdf', '.docx'))
                    )
    return


if __name__ == '__main__':
    # # 使用示例
    input_path = '/home/<USER>/project/large_model/材料/2023执行报告-九龙坡/气体压缩机械制造/重庆建设车用空调器有限责任公司-2023年.pdf'
    output_path = '/home/<USER>/project/large_model/材料/2023执行报告-九龙坡/气体压缩机械制造/convert_重庆建设车用空调器有限责任公司-2023年.docx'
    pdf_to_docx(input_path, output_path)

    # base_path = '/home/<USER>/project/large_model/材料/2023执行报告-九龙坡'
    # main(base_path)