# -*- coding: utf-8 -*-
"""
@File  : convert_doc_2_docx.py
@Author: <PERSON>
@Date  : 2025/1/15 10:22
@Desc  : 在windows电脑上执行脚本
"""
import os
import traceback
from datetime import datetime
import win32com.client
from docx import Document

from lib.check.base import listdir


def print_with_time(*args, **kwargs):
    current_time = datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"{formatted_time} ", end='')
    print(*args, **kwargs)


def convert_doc_to_docx(doc_path, docx_path):
    try:
        word = win32com.client.Dispatch("Word.Application")
        word.Visible = False
        doc = word.Documents.Open(doc_path)
        doc.SaveAs(docx_path, FileFormat=16)  # 16 表示保存为 .docx 格式
    finally:
        doc.Close()
        word.Quit()


def read_docx(docx_path):
    doc = Document(docx_path)
    return "\n".join([p.text for p in doc.paragraphs])


def remove_cache(base_path):
    for ent in listdir(base_path):
        path = os.path.join(base_path, ent)
        if not os.path.isdir(path):
            continue
        for file in os.listdir(path):
            if file.startswith('~$'):
                doc_path = os.path.join(path, file)
                os.remove(doc_path)
                print_with_time(f'remove {os.path.join(ent, file)}')


def doc_2_docx(base_path):
    """
    将指定路径下的.doc文件转换为.docx文件。

    该函数遍历指定路径下的所有文件夹，检查每个文件夹中是否存在未转换的.doc文件，
    并将其转换为.docx格式。如果转换过程中遇到错误，则记录错误信息并尝试重新转换。

    参数:
    base_path (str): 包含需要转换的文件的基路径。
    """
    # 获取并排序文件夹列表
    ents = sorted(listdir(base_path))
    for ent in ents:
        # 构建完整路径
        path = os.path.join(base_path, ent)
        # 如果不是目录，则跳过
        if not os.path.isdir(path):
            continue
        # 打印处理信息
        print_with_time(f'processing {ent} ...')
        # 如果文件夹中已存在.docx文件，则跳过
        if '.docx' in ''.join(listdir(path)):
            continue
        # 遍历文件夹中的所有文件
        for file in listdir(path):
            # 仅处理以.doc结尾的文件
            if file.endswith('.doc'):
                # 构建.doc文件的完整路径
                doc_path = os.path.join(path, file)
                # 构建目标.docx文件的完整路径
                docx_path = os.path.join(path, file.replace('.doc', '.docx'))
                # 尝试转换文件，最多重试5次
                for i in range(5):
                    try:
                        # 尝试将.doc文件转换为.docx格式
                        convert_doc_to_docx(doc_path, docx_path)
                        break
                    except Exception as e:
                        # 记录异常信息
                        print_with_time(type(e).__name__ + ' : ' + str(e))
                        continue


if __name__ == "__main__":
    base_path = r'\\wsl$\Ubuntu\home\liuzhengyi\project\large_model\srv-pollution-jiulongpo-emission\材料\2022年环评项目电子档案资料'
    remove_cache(base_path)
    doc_2_docx(base_path)