# -*- coding: utf-8 -*-
"""
@File  : llm.py
@Author: <PERSON>
@Date  : 2025/2/18 16:17
@Desc  : 
"""
import os
from ragflow_sdk import RAGFlow, DataSet, Chat
import warnings
from copy import deepcopy
import time
import requests
import random
import string
import json

from lib.llm import API_KEY, EMB_API, EMB_MODEL, LLM_API, LLM_NAME, RAG_API

CHUNK_SIZE = 512
PRINT_LOG = True

# RAG_API = "http://172.17.222.223:9380"
# API_KEY = "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
# EMB_MODEL = "rjmalagon/gte-qwen2-1.5b-instruct-embed-f16"
# EMB_API = "http://183.220.37.46:8051"
# LLM_NAME = "deepseek-r1-distill-qwen-14b-awq"
# LLM_API = "http://183.220.37.46:8081/v1"


def create_rag_obj(api_key="ragflow-adft4hYwOTExZWZiNzVhMDI0Mm",
                   base_url=RAG_API):
    return RAGFlow(api_key=api_key, base_url=base_url)


class Dataset:
    def __init__(self, rag_obj):
        self.rag_obj = rag_obj
        self.name_id = self._get_name_id()

    def _get_name_id(self):
        _map = {}
        for dataset in self.rag_obj.list_datasets():
            _map[dataset.name] = dataset.id
        return _map

    def delete(self, names=None, ids=None):
        """ 删除大数据集 """
        if ids:
            self.rag_obj.delete_datasets(ids=ids)
        elif names:
            if isinstance(names, str):
                names = [names]
            ids = [self.name_id[name] for name in names]
            self.rag_obj.delete_datasets(ids=ids)
        self.name_id = self._get_name_id()

    def list_datasets(self, *args, only_name=False, **kwargs):
        """ 获取大数据集 """
        if only_name:
            return [dataset.name for dataset in self.rag_obj.list_datasets(*args, **kwargs)]
        return self.rag_obj.list_datasets(*args, **kwargs)

    def create_dataset(self, name):
        dataset = self.rag_obj.create_dataset(
            name=name,
            language='Chinese',
            permission='team',
            chunk_method='naive',
            embedding_model=EMB_MODEL+'___OpenAI-API',
            parser_config=DataSet.ParserConfig(
                self.rag_obj,
                {'chunk_token_num': CHUNK_SIZE,
                'delimiter': '\\n!?;。；！？',
                'html4excel': False,
                'layout_recognize': True,
                'raptor': {'use_raptor': False}}
            )
        )
        self.name_id[dataset.name] = dataset.id
        return dataset

    def parse_dataset(self, name=None, dataset_id=None):
        """ 解析指定数据集中的所有文件 """
        if not name and not dataset_id:
            raise ValueError("name or dataset_id must be provided")
        if name and dataset_id:
            assert self.name_id[name] == dataset_id
        if name:
            dataset = self.list_datasets(name=name)[0]
        else:
            dataset = self.list_datasets(id=dataset_id)[0]
        documents = dataset.list_documents()
        for i in documents:
            print(i)
        ids = []
        for document in documents:
            ids.append(document.id)
        print('len(ids)', len(ids))
        dataset.async_parse_documents(ids)
        # dataset.async_cancel_parse_documents(ids)
        if PRINT_LOG:
            print("Async bulk parsing ...")
        wait = set(ids)
        done = set()
        fail = set()
        while wait or fail:
            for doc_id in deepcopy(wait):
                doc = dataset.list_documents(id=doc_id)[0]
                if doc.run == 'DONE':
                    if PRINT_LOG:
                        print(f"{doc.id} ({doc.name}) parsing done.")
                    done.add(doc.id)
                    wait.remove(doc.id)
                elif doc.run == 'FAIL':
                    fail.add(doc.id)
                    wait.remove(doc.id)
            if not wait and fail:
                wait = fail
                fail = set()
                for i in dataset.list_documents():
                    print(i)
                if PRINT_LOG:
                    print("Restart async bulk parsing ...")
                dataset.async_cancel_parse_documents(list(wait))
                dataset.async_parse_documents(list(wait))
                break
            time.sleep(1)


def upload_docs(dataset, paths):
    """
    将指定路径字符串的文件加载进数据集中
    dataset: rag dataset obj
    paths: str / list, 文件完整路径字符串
    """
    if isinstance(paths, str):
        paths = [paths]
    lst = []
    for path in paths:
        file_name = os.path.basename(path)
        with open(path, "rb") as file:
            binary_data = file.read()
        lst.append({
            'display_name': file_name,
            'blob': binary_data
        })
    dataset.upload_documents(lst)


def _upload_docs(dataset, root_path):
    """ 离线从文件夹中加载文件 """
    folders = [os.path.join(root_path, folder)
               for folder in os.listdir(root_path)
               if os.path.isdir(os.path.join(root_path, folder))]
    full_path = []
    for folder in folders:
        files = [file_name for file_name in os.listdir(folder)
                 if file_name.endswith('.docx') and not file_name.startswith('~$')]
        if 'flatten' in ''.join(files):
            files = [file_name for file_name in files if file_name.startswith('flatten')]
        if len(files) == 1:
            full_path.append(os.path.join(folder, files[0]))
        else:
            warnings.warn(f"文件夹 {folder} 含文件{files}")

    upload_docs(dataset, full_path)
    return full_path


def pipeline_docs(rag_obj, dataset_name, path):
    """ 创建数据集，将指定路径的所有文件加载并解析为chunk """
    datasets = Dataset(rag_obj)
    if dataset_name in datasets.list_datasets(only_name=True):
        datasets.delete(dataset_name)
    dataset = datasets.create_dataset(dataset_name)
    # for i in datasets.list_datasets():
    #     print(i)
    dataset = datasets.list_datasets(name=dataset_name)[0]
    _upload_docs(dataset, path)
    datasets.parse_dataset(dataset_name)
    for doc in dataset.list_documents():
        # if doc.run != 'DONE':
        print(doc)
    return dataset


def get_token():

    HOST_ADDRESS = RAG_API
    EMAIL = "<EMAIL>"
    PASSWORD = '''ctAseGvejiaSWWZ88T/m4FQVOpQyUvP+x7sXtdv3feqZACiQleuewkUi35E16wSd5C5QcnkkcV9cYc8TKPTRZlxappDuirxghxoOvFcJxFU4ixLsD
fN33jCHRoDUW81IH9zjij/vaw8IbVyb6vuwg6MX6inOEBRRzVbRYxXOu1wkWY6SsI8X70oF9aeLFp/PzQpjoe/YbSqpTq8qqrmHzn9vO+yvyYyvmDsphXe
X8f7fp9c7vUsfOCkM+gHY3PadG+QHa7KI7mzTKgUTZImK6BZtfRBATDTthEUbbaTewY4H0MnWiCeeDhcbeQao6cFy1To8pE3RpmxnGnS8BsBn8w=='''

    def register():
        url = HOST_ADDRESS + "/v1/user/register"
        name = "user"
        register_data = {"email": EMAIL, "nickname": name, "password": PASSWORD}
        res = requests.post(url=url, json=register_data)
        res = res.json()
        if res.get("code") != 0:
            raise Exception(res.get("message"))

    def login():
        url = HOST_ADDRESS + "/v1/user/login"
        login_data = {"email": EMAIL, "password": PASSWORD}
        response = requests.post(url=url, json=login_data)
        res = response.json()
        if res.get("code") != 0:
            raise Exception(res.get("message"))
        auth = response.headers["Authorization"]
        return auth

    def get_api_key_fixture():
        try:
            register()
        except Exception as e:
            print(e)
        auth = login()
        url = HOST_ADDRESS + "/v1/system/new_token"
        auth = {"Authorization": auth}
        response = requests.post(url=url, headers=auth)
        res = response.json()
        if res.get("code") != 0:
            raise Exception(res.get("message"))
        return auth['Authorization'], res["data"].get("token"), res["data"].get("tenant_id")

    if os.path.exists("llm_key"):
        try:
            with open("llm_key", "r") as f:
                token, tenant_id = f.read().split('\n')
                auth = login()
        except:
            auth, token, tenant_id = get_api_key_fixture()
            with open("llm_key", "w") as f:
                f.write(f"{token}\n{tenant_id}")
    else:
        auth, token, tenant_id = get_api_key_fixture()
        with open("llm_key", "w") as f:
            f.write(f"{token}\n{tenant_id}")
    return auth, token, tenant_id


def add_embed_model(auth, url=None, api_key=None):
    url = url or f"{RAG_API}/v1/llm/add_llm"
    header = {"Authorization": auth}
    data = {
        "llm_factory": "OpenAI-API-Compatible",
        "llm_name": EMB_MODEL,
        "model_type": "embedding",
        "api_base": EMB_API,
        "max_tokens": 128000
    }
    # if api_key:
    #     data['api_key'] = api_key
    res = requests.post(url=url, headers=header, json=data)
    return res.json()


def add_chat_model(auth, url=None, api_key=None):
    url = url or f"{RAG_API}/v1/llm/add_llm"
    header = {"Authorization": auth}
    data = {
        "llm_factory": "OpenAI-API-Compatible",
        "llm_name": LLM_NAME,
        "model_type": "chat",
        "api_base": LLM_API,
        "max_tokens": 128000
    }
    if api_key:
        data['api_key'] = api_key
    res = requests.post(url=url, headers=header, json=data, verify=False)
    return res.json()


def select_model(auth, tenant_id):
    url = f"{RAG_API}/v1/user/set_tenant_info"
    header = {"Authorization": auth}
    data = {
        "tenant_id": tenant_id,
        "llm_id": LLM_NAME+'___OpenAI-API',
        "embd_id": EMB_MODEL+'___OpenAI-API',
        "asr_id": "",
        "img2txt_id": ""
    }
    res = requests.post(url=url, headers=header, json=data)
    return res.json()


def create_assistant(name, rag_obj, dataset_id):
    assistant = rag_obj.create_chat(
        name,
        dataset_ids=[dataset_id],
        llm=Chat.LLM(rag_obj, {
            "model_name": LLM_NAME + '___OpenAI-API',
            "temperature": 0.3,
            "top_p": 0.3,
            "presence_penalty": 0.4,
            "frequency_penalty": 0.7,
            # "max_tokens": 128000,
        }),
        prompt=Chat.Prompt(rag_obj, {
            "similarity_threshold": 0.2,
            "keywords_similarity_weight": 0.7,
            "top_n": 8,
            "top_k": 1024,
            "variables": [{
                "key": "knowledge",
                "optional": False
            }],
            "rerank_model": "",
            "empty_response": None,
            "opener": "你好！ 我是你的助理，有什么可以帮到你的吗？",
            "show_quote": True,
            "prompt": (
                "你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据详细回答。当所有知识库内容都与问题无关时，"
                "你的回答必须包括“知识库中未找到您要的答案！”这句话。回答不需要考虑聊天历史。\n"
                "以下是知识库：\n"
                "{knowledge}\n"
                "以上是知识库。"
            )
        })
    )
    return assistant


def remove_assistant(rag_obj):
    try:
        ids = rag_obj.list_chats(page=1, page_size=100)
        if ids:
            ids = [i.id for i in ids]
            rag_obj.delete_chats(ids=ids)
    except:
        pass


def set_dialog(auth, dataset_id):
    url = RAG_API + "/v1/dialog/set"
    headers = {
        "Content-Type": "application/json",
        "Authorization": auth
    }
    set_data = {
        'name': 'rockontrol',
        'language': 'Chinese',
        'kb_ids': [dataset_id],
        'llm_id': LLM_NAME + '___OpenAI-API',
        'llm_setting': {
            'temperature': 0.4,
            'top_p': 0.6,
            'presence_penalty': 0.02,
            'frequency_penalty': 0.02,
        },
        'prompt_config': {
            'empty_response': '',
            'keyword': False,
            'parameters': [{
                'key': 'knowledge',
                'optional': False
            }],
            'prologue': '你好！我是你的助理，有什么可以帮到你的吗？',
            'quote': False,
            'refine_multiturn': False,
            'system': (
                "你是一个智能助手，请根据知识库的内容来回答问题，请列举知识库中的数据详细回答。当所有知识库内容都与问题无关时，"
                "你的回答必须包括“知识库中未找到您要的答案！”这句话。回答不需要考虑聊天历史。\n"
                "以下是知识库：\n"
                "{knowledge}\n"
                "以上是知识库。"
            ),
            'tts': False,
            'use_kg': False,
            'similarity_threshold': 0.2,
            'top_n': 8,
            'vector_similarity_weight': 0.3
        }
    }
    response = requests.post(url=url, headers=headers, json=set_data)
    res = response.json()
    if res.get("code") != 0:
        raise Exception(res.get("message"))
    dialog_id = res['data']['id']
    return dialog_id


def generate_conversation_id(length=32):
    characters = string.hexdigits[:-6]  # 只包含 0-9 和 a-f
    return ''.join(random.choice(characters) for _ in range(length))


def set_conversation(auth, dialog_id, conversation_id):
    url = RAG_API + "/v1/conversation/set"
    headers = {
        "Content-Type": "application/json",
        "Authorization": auth
    }
    set_data = {
        'dialog_id': dialog_id,
        'conversation_id': conversation_id,
        'name': 'conversation',
        'is_new': True
    }
    response = requests.post(url=url, headers=headers, json=set_data)
    res = response.json()
    if res.get("code") != 0:
        raise Exception(res.get("message"))
    return res['data']['message'][0]['content']


def chat(auth, conversation_id, question):
    url = RAG_API + "/v1/conversation/completion"
    headers = {
        "Content-Type": "application/json",
        "Authorization": auth
    }
    chat_data = {
        'conversation_id': conversation_id,
        'messages': [
            {
                "role": "assistant",
                "content": "你好！ 我是你的助理，有什么可以帮到你的吗？"
            },{
                "role": "user",
                "content": question
            }
        ]
    }
    response = requests.post(url=url, headers=headers, json=chat_data, stream=True, verify=False)
    if response.status_code == 200:
        # 持续读取响应流
        ans = ''
        for line in response.iter_lines():
            if line:
                # 解析每一行事件数据
                event_data = line.decode('utf-8')
                event_data = json.loads(event_data[5:])['data']
                if event_data == True:
                    return ans
                else:
                    print(event_data['answer'][len(ans):], end='')
                    ans = event_data['answer']
        return ans
    else:
        print(f"Request failed with status code: {response.status_code}")


def main():
    # 生成auth, token, tenant_id
    auth, token, tenant_id = get_token()
    print('auth', auth)
    print('token', token)
    print('tenant_id', tenant_id)
    # 注册模型
    response = add_embed_model(auth)
    print(response)
    response = add_chat_model(auth, api_key=API_KEY)
    print(response)
    response = select_model(auth, tenant_id)
    print(response)
    rag_obj = create_rag_obj(api_key=token)
    # dataset = pipeline_docs(rag_obj, '环评', '/home/<USER>/project/large_model/材料/extract-环评')
    class dataset: id = "1ed033e0049811f096dd0242ac130006"
    print('dataset.id', dataset.id)
    remove_assistant(rag_obj)

    dialog_id = set_dialog(auth, dataset.id)
    conversation_id = generate_conversation_id()
    session = set_conversation(auth, dialog_id, conversation_id)
    ans = chat(auth, conversation_id, '请详细介绍下关键工序窄口拓宽项目')
    print('\n', '-'* 200)
    ans = chat(auth, conversation_id, '关键工序窄口拓宽项目的地理坐标是多少？')
    print('\n', '-'* 200)
    ans = chat(auth, conversation_id, '关键工序窄口拓宽项目与园区规划环评的符合性分析')


    # assistant = create_assistant('rockontrol', rag_obj, dataset.id)
    # session = assistant.create_session('conversation1')
    #
    # print("\n==================== AI =====================\n")
    # print("你好！ 我是你的助理，有什么可以帮到你的吗？")
    #
    # while True:
    #     question = input("\n==================== User =====================\n> ")
    #     print("\n==================== AI =====================\n")
    #
    #     cont = ""
    #     full_ans = ""
    #     for ans in session.ask(question, stream=True):
    #         full_ans = ans.content
    #         print(full_ans)
    #         print('-'*100)


if __name__ == '__main__':
    main()

