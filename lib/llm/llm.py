# -*- coding: utf-8 -*-
"""
@File  : llm.py.py
@Author: <PERSON>
@Date  : 2025/3/19 11:11
@Desc  : 
"""
import requests
from lib.llm import LLM_API, LLM_NAME, API_KEY, CHANGE_TOKEN
from lib.request_token import get_llm_token

# 配置信息
# LLM_NAME = "deepseek-r1-distill-qwen-14b-awq"
# LLM_API = "http://183.220.37.46:8081/v1"
# API_KEY = "cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"


def ask_llm(question):
    """
    向LLM模型发送问题并获取回答
    :param question: 用户问题（字符串）
    :return: 模型生成的回答
    """
    if CHANGE_TOKEN:
        headers = {
            "Content-Type": "application/json",
            "X-Auth-Token": get_llm_token()
        }
    else:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }

    data = {
        "model": LLM_NAME,
        "messages": [
            {"role": "user", "content": question}
        ]
    }

    try:
        response = requests.post(
            url=f"{LLM_API}/chat/completions",  # 假设使用标准chat completions端点
            headers=headers,
            json=data,
            verify=False
        )

        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            return f"请求失败，状态码：{response.status_code}，错误信息：{response.text}"

    except Exception as e:
        return f"发生异常：{str(e)}"


# 示例使用
if __name__ == "__main__":
    response = ask_llm('你好')
    print("\n模型回复：")
    print(response)