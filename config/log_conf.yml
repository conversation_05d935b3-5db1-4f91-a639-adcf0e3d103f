version: 1

formatters:
    file_formater:
        style: '{'
        format: '{process:5d}:{thread:5d} - {asctime:s} - {filename:>17s}[line:{lineno:>4d}] - {levelname:<8s}: {message:s}'
#        datefmt: '%Y-%m-%d %H:%M:%S'
    console_formater:
        style: '{'
        format: '{asctime:s} - {filename:>17s}[line:{lineno:>4d}] - {levelname:<8s}: {message:s}'
        datefmt: '%Y-%m-%d %H:%M:%S'

.encoding: &encoding utf8

.default: &default
    formatter: file_formater
    backupCount: 10
    encoding: *encoding

handlers:
    console_handler:
        level: INFO
        class: logging.StreamHandler
        formatter: console_formater
        stream: ext://sys.stdout
    debug_handler:
        level: DEBUG
        class: logging.handlers.TimedRotatingFileHandler
        filename: debug.log
        when: 'H'
        interval: 2
        <<: *default
    info_handler:
        level: INFO
        class: logging.handlers.TimedRotatingFileHandler
        filename: info.log
        when: 'H'
        interval: 2
        <<: *default
    error_handler:
        level: ERROR
        class: logging.handlers.TimedRotatingFileHandler
        filename: error.log
        when: 'H'
        interval: 2
        <<: *default

loggers:
    root:
        level: DEBUG
        handlers: [console_handler, debug_handler, info_handler, error_handler]
    uvicorn.access:
        level: DEBUG
        handlers: [console_handler, debug_handler, info_handler, error_handler]
        propagate: false
