# -*- coding: utf-8 -*-
"""
@File  : utils.py
@Author: <PERSON>
@Date  : 2025/3/3 17:49
@Desc  : 
"""
import os
import shutil
from multiprocessing import shared_memory
import fcntl
import hashlib
import threading
import traceback

from lib import logger, proj_dir, Global
from lib.check.base import clean_null
from lib.check.check_commit_time import run as check_commit_time, run_warn as check_commit_time_warn
from lib.check.check_mqy_mismatch import online_check_ents
from lib.check.check_no_change_val import dbbase_run as check_no_change_val
from lib.check.check_year_emission import run as check_year_emission
from lib.check.check_year_emission_hp import run as check_year_emission_hp
from lib.check.check_hospital_bed import check_hospital_online
from lib.check.check_oil import check_volume, check_quantity, get_data
from lib.check.check_port_base_info import check_absence_port, check_gas_port
from lib.check.check_exe_with_self_monitor import check_exe_with_self_monitor
from lib.check.check_exec_online_monitor_quarter import check_exec_online_monitor_mismatch
from lib.check.check_exe_online_exceeding import check_exe_online_exceeding
from lib.check.check_permit_eia_emission_threshold import run as check_permit_eia_emission_threshold
from lib.extract.main import ent_link_pipeline, Exe_Link_Pipeline
from lib.transition_docx_structure import run as flatten_docx
from mod.load_and_save import load_json, save_json
from serve import BASE_PATH


check_function = [
    'check_yqm_absence',
    'check_pollution_emission_mismatch',
    'check_constant_value_all',
    'check_constant_value_one'
]


def run(city, start_time, end_time, function):
    res = {}
    for func in function:
        logger.info(f'{func} is running...')
        if func not in check_function:
            continue
            logger.info(f'{func} is not implemented.')
        elif func == 'check_yqm_absence':
            exception = check_commit_time(city, start_time, end_time)
        elif func == 'check_pollution_emission_mismatch':
            exception = online_check_ents(city, start_time, end_time)
        elif func == 'check_constant_value_all':
            exception = check_no_change_val(city, start_time, end_time, table=True)
        elif func == 'check_constant_value_one':
            exception = check_no_change_val(city, start_time, end_time, table=False)
        res[func] = exception
        logger.info(f'{func} is done.')

    return res


def run_warning(city, start_time, end_time):
    res = {}
    logger.info('run_warning is running...')
    exception = check_commit_time_warn(city, start_time, end_time)
    clean_null(exception)
    logger.info(f'run_warning is done.')
    return exception


def run_year_emission(city, years):
    logger.info('run_year_emission is running...')
    if city in ['上海市', '九龙坡区', '北京市', '呼和浩特市', '天津市', '太原市']:
        exception = check_year_emission(city, years)
    else:
        exception = []
    logger.info(f'run_year_emission is done.')
    return exception


def run_hosipital_bed(city):
    logger.info('run_hosipital_bed is running...')
    if city == '九龙坡区':
        exception = check_hospital_online(city)
    else:
        exception = []
    logger.info('run_hosipital_bed is done.')
    return exception


def run_oil_quantity(city):
    logger.info('run_oil_quantity is running...')
    if city == '九龙坡区':
        permit, eia = get_data(city)
        exception = check_quantity(permit, eia)
    else:
        exception = {}
    logger.info('run_oil_quantity is done.')
    return exception


def run_oil_volume(city):
    logger.info('run_oil_volume is running...')
    if city == '九龙坡区':
        permit, eia = get_data(city)
        exception = check_volume(permit, eia)
    else:
        exception = []
    logger.info('run_oil_volume is done.')
    return exception


def run_port_base_info(city):
    logger.info('run_port_base_info is running...')
    if city == '九龙坡区':
        exception = check_absence_port(city)
    else:
        exception = []
    logger.info('run_port_base_info is done.')
    return exception


def run_gas_port(city):
    logger.info('run_gas_port is running...')
    if city == '九龙坡区':
        exception = check_gas_port(city)
    else:
        exception = []
    logger.info('run_gas_port is done.')
    return exception


def run_year_emission_vs_hp(city, years):
    """
    检查年度执行报告污染物排放量是否超过环评总量控制指标

    Args:
        city (str): 城市名称
        years (list): 检查年份列表

    Returns:
        dict: 检查结果，包含超标线索清单
    """
    logger.info('run_year_emission_vs_hp is running...')
    if city == '九龙坡区':
        exception = check_year_emission_hp(city, years)
    else:
        exception = []
    logger.info('run_year_emission_vs_hp is done.')
    return exception


def run_exe_with_self_monitor(city, years=None):
    """
    执行报告与自行监测数据校验

    Args:
        city (str): 城市名称
        years (list, optional): 年份列表，如[2023, 2024]

    Returns:
        list: 异常记录列表
    """
    logger.info(f'run_exe_with_self_monitor is running...')
    if city == '九龙坡区':
        exception = check_exe_with_self_monitor(city, years)
    else:
        exception = []
    logger.info(f'run_exe_with_self_monitor is done.')
    return exception


def run_exec_online_monitor_mismatch(city, start_time, end_time):
    """
    季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记不匹配检查

    Args:
        city (str): 城市名称
        start_time (str): 查询开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
        end_time (str): 查询结束时间，格式为 'YYYY-MM-DD HH:MM:SS'

    Returns:
        list: 不匹配的记录列表
    """
    logger.info(f'run_exec_online_monitor_mismatch is running...')
    if city == '九龙坡区':
        exception = check_exec_online_monitor_mismatch(start_time, end_time, city)
    else:
        exception = []
    logger.info(f'run_exec_online_monitor_mismatch is done.')
    return exception


def run_exe_online_exceeding(city, start_date, end_date):
    """
    执行报告与在线监测超标数据校验

    校验在线监测企业废水废气的排口超标监测指标、超标时段、超标数据是否都在执行报告中有记录。

    Args:
        city (str): 城市名称
        start_date (str): 起始日期，格式为 'YYYY-MM-DD'
        end_date (str): 结束日期，格式为 'YYYY-MM-DD'

    Returns:
        list: 存在问题的记录数组，每个元素包含同一条在线监测记录的所有问题
    """
    logger.info(f'run_exe_online_exceeding is running...')
    if city == '九龙坡区':
        exception = check_exe_online_exceeding(city, start_date, end_date)
    else:
        exception = []
    logger.info(f'run_exe_online_exceeding is done.')
    return exception


def run_permit_eia_emission_threshold(city, start_date, end_date):
    """
    许可证记载的污染物全厂排放口总计或排口浓度或排口速率大于环评记载的排放限值检查

    Args:
        city (str): 城市名称
        start_date (str): 起始时间，格式：'YYYY-MM-DD'
        end_date (str): 结束时间，格式：'YYYY-MM-DD'

    Returns:
        dict: 包含三种线索的字典
    """
    logger.info('run_permit_eia_emission_threshold is running...')
    if city == '九龙坡区':
        exception = check_permit_eia_emission_threshold(city, start_date, end_date)
    else:
        exception = {
            "total_emission_clues": [],
            "concentration_clues": [],
            "emission_rate_clues": []
        }
    logger.info('run_permit_eia_emission_threshold is done.')
    return exception


def run_extract_graph(file1_type, file2_type, file1_name, file2_name, file1_data, file2_data):
    for _type, file_name, file_data in [
        [file1_type, file1_name, file1_data],
        [file2_type, file2_name, file2_data]
    ]:
        path = os.path.join(BASE_PATH, _type)
        if os.path.exists(path):
            shutil.rmtree(path)
        os.mkdir(path)
        path = os.path.join(path, 'data')
        os.mkdir(path)
        file_path = os.path.join(path, file_name)
        with open(file_path, 'wb') as f:
            f.write(file_data)

    if file1_type == '执行报告' or file2_type == '执行报告':
        file1_path = os.path.join(BASE_PATH, file1_type, 'data')
        file2_path = os.path.join(BASE_PATH, file2_type, 'data')
        answer = Exe_Link_Pipeline(
            file1_path, file1_type, file1_name, file2_path, file2_type, file2_name
        ).run()
    else:
        file1_path = os.path.join(BASE_PATH, file1_type)
        file2_path = os.path.join(BASE_PATH, file2_type)
        flatten_docx(file1_path)
        flatten_docx(file2_path)
        answer = ent_link_pipeline(
            file1_path, file1_type, file1_name, file2_path, file2_type, file2_name
        )
    return answer


def create_lock_file():
    for arg in ['ids', 'cnt', 'slots']:
        try:
            data = shared_memory.ShareableList(name=arg)
            data.shm.unlink()
            data.shm.close()
        except:
            pass

    file_path = os.path.join(proj_dir, 'bin', '.lock')
    if os.path.exists(file_path):
        os.remove(file_path)
    with open(file_path, 'w') as file:
        pass


class Lock:
    def __init__(self, lock_file=os.path.join(proj_dir, 'bin', '.lock')):
        """
        初始化锁文件
        :param lock_file: 锁文件路径
        """
        self.lock_file = lock_file
        self.file_handle = None

    def acquire(self):
        """
        获取文件锁，如果锁已经被占用，则阻塞等待直到锁被释放
        """
        if not self.file_handle:
            # 打开锁文件
            self.file_handle = open(self.lock_file, 'w')

        # 尝试获取锁，阻塞模式
        fcntl.flock(self.file_handle, fcntl.LOCK_EX)  # 独占锁，阻塞等待

    def release(self):
        """
        释放文件锁
        """
        if self.file_handle:
            # 释放锁
            fcntl.flock(self.file_handle, fcntl.LOCK_UN)
            # 关闭文件句柄（可选，如果文件锁长期使用可以不用关闭）
            self.file_handle.close()
            self.file_handle = None


def calc_web_data_md5(data):
    md5_hash = hashlib.md5()
    md5_hash.update(data)
    return md5_hash.hexdigest()


def calc_file_md5(file_path):
    md5_hash = hashlib.md5()
    with open(file_path, "rb") as file:
        # 逐块读取文件，防止大文件占用内存
        for chunk in iter(lambda: file.read(4096), b""):
            md5_hash.update(chunk)
    return md5_hash.hexdigest()


def calc_str_md5(chars: str):
    md5_hash = hashlib.md5(chars.encode('utf-8'))  # 将字符串编码为字节
    return md5_hash.hexdigest()  # 返回十六进制字符串


def calc_files_md5(file_paths: list):
    md5_lst = []
    for file_path in file_paths:
        md5_hash = calc_file_md5(file_path)
        md5_lst.append(md5_hash)
    md5_lst = sorted(md5_lst)
    md5_str = ''.join(md5_lst)
    md5_hash = calc_str_md5(md5_str)
    return md5_hash


class TaskManager:
    def __init__(self, max_task=1):
        self.max_task = max_task
        self.lock = Lock()
        self.lock.acquire()
        try:
            self.running_ids = shared_memory.ShareableList(['a'*256 for i in range(max_task)], name='ids')
        except:
            self.running_ids = shared_memory.ShareableList(name='ids')
        try:
            self.running_cnt = shared_memory.ShareableList([0], name='cnt')
        except:
            self.running_cnt = shared_memory.ShareableList(name='cnt')
        try:
            self.slots = shared_memory.ShareableList([0 for i in range(max_task)], name='slots')
        except:
            self.slots = shared_memory.ShareableList(name='slots')
        self.lock.release()

    def get_task_result(self, Id):
        self.lock.acquire()
        path = os.path.join(proj_dir, 'data', 'graph_jobs')
        if not os.path.exists(path):
            os.mkdir(path)

        # 缓存搜索
        files = os.listdir(path)
        if Id + '.json' in files:
            file_path = os.path.join(path, Id + '.json')
            self.lock.release()
            result = {
                "id": Id,
                "data": load_json(file_path),
                "message": "",
            }
            return True, result
        else:
            if Id in self.running_ids:
                self.lock.release()
                result = {
                    "id": Id,
                    "data": "",
                    "message": f"当前任务{Id}正在计算。"
                }
                return True, result
            else:
                self.lock.release()
                return False, f'没有找到任务{Id}。'

    def add_task(self, Id):
        self.lock.acquire()
        # 并行数达到上限
        if self.running_cnt[0] == self.max_task:
            self.lock.release()
            raise RuntimeError('当前运行任务数已达上限，请稍后再试。')
        if Id in self.running_ids:
            self.lock.release()
            raise RuntimeError(f'当前任务{Id}正在计算。')
        else:
            for idx in range(self.max_task):
                if self.slots[idx] == 1:
                    continue
                self.slots[idx] = 1
                self.running_ids[idx] = Id
                self.running_cnt[0] += 1
                break
            self.lock.release()
            return Id

    def done(self, Id):
        self.lock.acquire()
        if Id not in self.running_ids:
            self.lock.release()
            return
        idx = self.running_ids.index(Id)
        self.slots[idx] = 0
        self.running_ids[idx] = ''
        self.running_cnt[0] -= 1
        self.lock.release()


def thread_run_extract_graph(file1_type, file2_type, file1_name, file2_name, file1_data, file2_data, task_manager, Id):
    try:
        res = run_extract_graph(file1_type, file2_type, file1_name, file2_name, file1_data, file2_data)
        # res = func(file1_type, file2_type, file1_name, file2_name)
        file_path = os.path.join(proj_dir, f'data/graph_jobs/{Id}.json')
        save_json(file_path, res)
    except:
        logger.error(traceback.format_exc())
    finally:
        task_manager.done(Id)


def remove_files(Dir, Id):
    """ 删除缓存 """
    path = os.path.join(proj_dir, 'data', Dir)
    if not os.path.exists(path):
        return
    if Id.lower() == 'all':
        try:
           shutil.rmtree(path)
        except:
           pass
        os.makedirs(path, exist_ok=True)
        return
    if Id not in os.listdir(path):
        raise FileExistsError(f'没有找到缓存"{Id}"。')
    path = os.path.join(path, Id)
    if os.path.isdir(path):
        shutil.rmtree(path)
    elif os.path.isfile(path):
        os.remove(path)
    return

