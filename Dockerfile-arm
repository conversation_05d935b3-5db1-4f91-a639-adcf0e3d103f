# FROM hub-dev.rockontrol.com/rk-ai-tools/env-ml-python3-pytorch:0.0.0-ce310b6
FROM env-ml-python312-pytorch251-arm64-xinference:20250213

WORKDIR /workspace
ADD . /workspace

ENV LANG=zh_CN.UTF-8

# RUN set -xe && \
#    apt-get update -y && \
#    apt-get install -y python3-pip postgresql libpq-dev

RUN /opt/miniconda/bin/pip config --global set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    /opt/miniconda/bin/pip config --global set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    /opt/miniconda/bin/pip3 install -r /workspace/requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    rm -f requirements.txt && \
    /opt/miniconda/bin/pip cache purge

WORKDIR /workspace/bin

RUN rm -rf /workspace/bin/llm_key /workspace/bin/df_cache.pkl

# 不显示warning信息
ENTRYPOINT ["/opt/miniconda/bin/python3", "-W", "ignore", "web_server.py"]
CMD ["--mode", "formal", "--post", "true"]

