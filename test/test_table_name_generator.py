#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_table_name_generator.py
@Author: <PERSON>
@Date  : 2025/8/21
@Desc  : TableNameGenerator类的单元测试
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.check.table_name_generator import TableNameGenerator, get_table_name, get_excel_file_path


class TestTableNameGenerator(unittest.TestCase):
    """TableNameGenerator类的测试用例"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.generator = TableNameGenerator()
        
        # 模拟数据库查询结果
        self.mock_data = pd.DataFrame([
            {
                'city_code': '500000000',
                'city_name': '重庆市',
                'city_pinyin': 'chongqing',
                'province_code': '500000000',
                'province_name': '重庆市',
                'province_pinyin': 'chongqing'
            },
            {
                'city_code': '500107000',
                'city_name': '九龙坡区',
                'city_pinyin': 'jiulongpo',
                'province_code': '500000000',
                'province_name': '重庆市',
                'province_pinyin': 'chongqing'
            },
            {
                'city_code': '110000000',
                'city_name': '北京市',
                'city_pinyin': 'beijing',
                'province_code': '110000000',
                'province_name': '北京市',
                'province_pinyin': 'beijing'
            },
            {
                'city_code': '310000000',
                'city_name': '上海市',
                'city_pinyin': 'shanghai',
                'province_code': '310000000',
                'province_name': '上海市',
                'province_pinyin': 'shanghai'
            }
        ])
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_load_city_province_mapping(self, mock_get_db_cursor):
        """测试城市-省份映射加载功能"""
        # 模拟数据库连接和查询
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 加载映射
        self.generator._load_city_province_mapping()
        
        # 验证缓存已加载
        self.assertTrue(self.generator._cache_loaded)
        self.assertGreater(len(self.generator._cache), 0)
        
        # 验证具体映射
        self.assertEqual(self.generator._cache['九龙坡区'], 'chongqing')
        self.assertEqual(self.generator._cache['500107000'], 'chongqing')
        self.assertEqual(self.generator._cache['重庆市'], 'chongqing')
        self.assertEqual(self.generator._cache['北京市'], 'beijing')
        self.assertEqual(self.generator._cache['上海市'], 'shanghai')
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_get_province_pinyin_by_city_name(self, mock_get_db_cursor):
        """测试通过城市名获取省份拼音"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 测试城市名查询
        self.assertEqual(self.generator._get_province_pinyin('九龙坡区'), 'chongqing')
        self.assertEqual(self.generator._get_province_pinyin('重庆市'), 'chongqing')
        self.assertEqual(self.generator._get_province_pinyin('北京市'), 'beijing')
        self.assertEqual(self.generator._get_province_pinyin('上海市'), 'shanghai')
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_get_province_pinyin_by_city_code(self, mock_get_db_cursor):
        """测试通过城市编码获取省份拼音"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 测试城市编码查询
        self.assertEqual(self.generator._get_province_pinyin('500107000'), 'chongqing')
        self.assertEqual(self.generator._get_province_pinyin('500000000'), 'chongqing')
        self.assertEqual(self.generator._get_province_pinyin('110000000'), 'beijing')
        self.assertEqual(self.generator._get_province_pinyin('310000000'), 'shanghai')
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_get_province_pinyin_unknown_city(self, mock_get_db_cursor):
        """测试未知城市的处理"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 测试未知城市，应该返回默认值
        self.assertEqual(self.generator._get_province_pinyin('未知城市'), 'chongqing')
        self.assertEqual(self.generator._get_province_pinyin('999999999'), 'chongqing')
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_get_table_name(self, mock_get_db_cursor):
        """测试表名生成功能"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 测试表名生成
        base_table = "zxgk_basic_data_water_year_report"
        
        # 通过城市名生成表名
        table_name = self.generator.get_table_name(base_table, "九龙坡区")
        self.assertEqual(table_name, "zxgk_basic_data_water_year_report_chongqing")
        
        # 通过城市编码生成表名
        table_name = self.generator.get_table_name(base_table, "500107000")
        self.assertEqual(table_name, "zxgk_basic_data_water_year_report_chongqing")
        
        # 其他城市
        table_name = self.generator.get_table_name(base_table, "北京市")
        self.assertEqual(table_name, "zxgk_basic_data_water_year_report_beijing")
        
        table_name = self.generator.get_table_name(base_table, "上海市")
        self.assertEqual(table_name, "zxgk_basic_data_water_year_report_shanghai")
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_cache_mechanism(self, mock_get_db_cursor):
        """测试缓存机制"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 第一次调用，应该查询数据库
        self.generator.get_table_name("test_table", "九龙坡区")
        self.assertEqual(mock_db.query_sql.call_count, 1)
        
        # 第二次调用，应该使用缓存，不再查询数据库
        self.generator.get_table_name("test_table", "九龙坡区")
        self.assertEqual(mock_db.query_sql.call_count, 1)  # 调用次数不变
        
        # 清除缓存后再次调用，应该重新查询数据库
        self.generator.clear_cache()
        self.generator.get_table_name("test_table", "九龙坡区")
        self.assertEqual(mock_db.query_sql.call_count, 2)
    
    @patch('lib.check.table_name_generator.get_db_cursor')
    def test_convenience_functions(self, mock_get_db_cursor):
        """测试便捷函数"""
        mock_db = MagicMock()
        mock_db.query_sql.return_value = self.mock_data
        mock_get_db_cursor.return_value = mock_db
        
        # 测试get_table_name便捷函数
        table_name = get_table_name("test_table", "九龙坡区")
        self.assertEqual(table_name, "test_table_chongqing")
        
        # 测试get_excel_file_path便捷函数
        file_path = get_excel_file_path("basic_information_enterprise", "九龙坡区")
        self.assertTrue(file_path.endswith("basic_information_enterprise_chongqing.xlsx"))
    
    def test_get_cache_info(self):
        """测试缓存信息获取"""
        cache_info = self.generator.get_cache_info()
        
        self.assertIn('cache_loaded', cache_info)
        self.assertIn('cache_size', cache_info)
        self.assertIn('sample_mappings', cache_info)
        
        # 初始状态应该是未加载
        self.assertFalse(cache_info['cache_loaded'])
        self.assertEqual(cache_info['cache_size'], 0)


class TestTableNameGeneratorIntegration(unittest.TestCase):
    """TableNameGenerator的集成测试"""
    
    def test_real_database_connection(self):
        """测试真实数据库连接（需要数据库环境）"""
        try:
            generator = TableNameGenerator()
            
            # 尝试查询重庆相关的城市
            province_pinyin = generator._get_province_pinyin('九龙坡区')
            self.assertIsInstance(province_pinyin, str)
            self.assertGreater(len(province_pinyin), 0)
            
            # 测试表名生成
            table_name = generator.get_table_name("test_table", "九龙坡区")
            self.assertTrue(table_name.startswith("test_table_"))
            
            print(f"✅ 集成测试通过 - 九龙坡区对应省份拼音: {province_pinyin}")
            print(f"✅ 生成的表名示例: {table_name}")
            
        except Exception as e:
            print(f"⚠️ 集成测试跳过（需要数据库环境）: {e}")


if __name__ == '__main__':
    # 运行单元测试
    print("=== TableNameGenerator 单元测试 ===")
    unittest.main(verbosity=2)
