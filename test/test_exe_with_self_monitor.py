#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File  : test_exe_with_self_monitor.py
@Author: <PERSON>
@Date  : 2025/7/28
@Desc  : 测试执行报告与自行监测数据校验功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.check.check_exe_with_self_monitor import (
    calculate_daily_average_for_water,
    run
)

def test_daily_average_calculation():
    """测试废水日平均值计算功能"""
    print("=== 测试废水日平均值计算功能 ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'self_id': ['ENT001', 'ENT001', 'ENT001', 'ENT001', 'ENT002', 'ENT002'],
        'enterprise_name': ['企业A', '企业A', '企业A', '企业A', '企业B', '企业B'],
        'monitoring_site_data': ['排口1', '排口1', '排口1', '排口1', '排口2', '排口2'],
        'monitoring_indicator_data': ['COD', 'COD', 'COD', 'COD', 'COD', 'COD'],
        'monitoring_time': [
            '2024-01-01 08:00:00',
            '2024-01-01 14:00:00', 
            '2024-01-01 20:00:00',
            '2024-01-02 08:00:00',
            '2024-01-01 09:00:00',
            '2024-01-01 15:00:00'
        ],
        'monitoring_result': [10.5, 12.3, 11.8, 9.2, 15.6, 14.2],
        'data_type': ['waste_water'] * 6
    })
    
    print("原始测试数据:")
    print(test_data[['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 
                     'monitoring_time', 'monitoring_result']])
    print()
    
    # 计算日平均值
    daily_avg_result = calculate_daily_average_for_water(test_data)
    
    print("日平均值计算结果:")
    print(daily_avg_result[['self_id', 'monitoring_site_data', 'monitoring_indicator_data', 
                           'monitoring_time', 'monitoring_result']])
    print()
    
    # 验证结果
    expected_results = {
        ('ENT001', '排口1', 'COD', '2024-01-01'): (10.5 + 12.3 + 11.8) / 3,  # 11.533...
        ('ENT001', '排口1', 'COD', '2024-01-02'): 9.2,
        ('ENT002', '排口2', 'COD', '2024-01-01'): (15.6 + 14.2) / 2  # 14.9
    }
    
    print("验证结果:")
    for _, row in daily_avg_result.iterrows():
        date_str = row['monitoring_time'].strftime('%Y-%m-%d')
        key = (row['self_id'], row['monitoring_site_data'], 
               row['monitoring_indicator_data'], date_str)
        expected = expected_results.get(key)
        actual = row['monitoring_result']
        
        if expected is not None:
            diff = abs(expected - actual)
            status = "✅ 正确" if diff < 0.001 else "❌ 错误"
            print(f"{key}: 期望={expected:.3f}, 实际={actual:.3f} {status}")
        else:
            print(f"{key}: 未找到期望值")
    
    print()

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试执行报告与自行监测数据校验功能 ===")
    
    try:
        # 测试参数
        city = '九龙坡区'
        years = [2024]  # 测试2024年数据
        
        print(f"测试城市: {city}")
        print(f"测试年份: {years}")
        print()
        
        # 执行检查
        result = run(city, years)
        
        # 输出结果
        print("=== 检查结果 ===")
        print(f"总异常记录数: {len(result)}")
        
        # 按数据类型统计
        gas_count = len([r for r in result if r.get('data_type') == 'waste_gas'])
        water_count = len([r for r in result if r.get('data_type') == 'waste_water'])
        
        print(f"废气异常记录数: {gas_count}")
        print(f"废水异常记录数: {water_count}")
        print()
        
        # 按异常类型统计
        anomaly_types = {}
        for record in result:
            anomaly_type = record.get('anomaly_type', '未知')
            anomaly_types[anomaly_type] = anomaly_types.get(anomaly_type, 0) + 1
        
        print("=== 异常类型统计 ===")
        for anomaly_type, count in anomaly_types.items():
            print(f"{anomaly_type}: {count} 条")
        print()
        
        # 显示异常详情（前5条）
        if result:
            print("=== 异常详情（前5条）===")
            for i, record in enumerate(result[:5], 1):
                print(f"{i}. 企业: {record.get('f_enterprise_name', 'N/A')}")
                print(f"   数据类型: {record.get('data_type', 'N/A')}")
                print(f"   异常类型: {record.get('anomaly_type', 'N/A')}")
                print(f"   排口: {record.get('monitoring_site_data', 'N/A')}")
                print(f"   污染因子: {record.get('monitoring_indicator_data', 'N/A')}")
                print(f"   执行报告值: {record.get('exec_value', 'N/A')}")
                print(f"   自行监测值: {record.get('self_value', 'N/A')}")
                print(f"   差值: {record.get('difference', 'N/A')}")
                print()
        else:
            print("未发现异常记录")
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试执行报告与自行监测数据校验功能...")
    print()
    
    # 测试日平均值计算
    test_daily_average_calculation()
    
    # 测试基本功能
    success = test_basic_functionality()
    
    if success:
        print("✅ 所有测试完成")
    else:
        print("❌ 测试失败")
