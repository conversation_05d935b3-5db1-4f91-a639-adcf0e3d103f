apiVersion: "serving.octohelm.tech/v1alpha1"
kind: "QService"
metedata:
  name: ${{ PROJECT_NAME }}
spec:
  image: ${{ PROJECT_IMAGE }}
  readinessProbe:
    action: http://:8080/jiulongpo/liveness/
    initialDelaySeconds: 20
    periodSeconds: 60
  livenessProbe:
    action: http://:8080/jiulongpo/liveness/
    initialDelaySeconds: 20
    periodSeconds: 60
  ports:
    - "80"
  nodeSelector:
    #如果需要服务需要GPU资源，添加此标签，会调度至GPU节点
    #gpu_server: "false"
    #如果期望服务调度到指定节点，可添加对应标签，如：想固定调度至*************这台节点，可添加如下内容
    #kubernetes.io/hostname: *************
  resources:
    ##单位m,10m=0.01核 前面为最小需求资源，后面为最多使用资源
    #AI服务尽量不超过两核(2000m)
    cpu: 1000/2000m
    
    ##单位Mi 前面为最小需求资源，后面为最多使用资源
    # AI服务尽量不超过2G(2000Mi)
    memory: 1000/3000Mi
    
    #显存单位GiB 前面为最小需求资源，后面为最多使用资源
    #显存 = max(3, 向上取整(服务占用显存))
    #rps = 单pod的rps * 2/3
#    aliyun.com/gpu-mem: 1/1
