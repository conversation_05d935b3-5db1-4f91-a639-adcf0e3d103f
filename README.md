# srv-jiulongpo-word-check

九龙坡文件内容核查（执行报告/排污许可证/环评报告）


### docker command

```
docker build -t srv-jiulongpo-word-check .
docker run -d -p 15435:8080 --name jiulongpo-word-check-instance -v $(pwd)/data:/workspace/data --restart=always srv-jiulongpo-word-check --mode formal --post true
docker compose -f docker-compose.yml up -d --build
docker compose -f docker-compose.yml down -v
```

### release port
```
sudo kill -9 $(sudo lsof -t -i:8080)
```


### curl

```
curl -X GET http://localhost:15435/liveness \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"

curl -X POST http://localhost:15435/executive-report \
     -H "Content-Type: application/json" \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n" \
     -d '{"start_time": "2021-01-01", "end_time": "2023-02-25"}'

curl -G "http://localhost:15435/executive-report/warning?start_time=2021-01-01&end_time=2023-02-25" \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"
     
curl -X POST "http://localhost:15435/executive-report/licence/year" \
     -H "Content-Type: application/json" \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n" \
     -d '{"time": [2021, 2022, 2023]}'
     
curl -X POST "http://localhost:15435/llm/extract-graph" \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n" \
     -F "file1_type=许可证" \
     -F "file2_type=环评" \
     -F "file1=@/data/lzy/【许可证】国鸿氢能科技有限公司.docx" \
     -F "file2=@/data/lzy/【环评】国鸿氢能源环评.docx"
     
curl -G "http://localhost:15435/all"   \
     -H "X-token: cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n"   \
     --data-urlencode "city=九龙坡区"   \
     -d "start_time=2024-01-01"   \
     -d "end_time=2025-06-25"   \
     -d "time_str=20241,2022,2023"   \
     -d "rerun=1"
     
# 验证智慧研判大模型服务是否正常
curl -X POST "http://*************:8081/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n" \
  -d '{
    "model": "deepseek-r1-distill-qwen-14b-awq",
    "messages": [
      {"role": "user", "content": "你好"}
    ],
    "temperature": 0.7
  }'
  
# 验证九龙坡大模型服务是否正常 
TOKEN=$(python3 -c '
import requests, json;
def get_llm_token():
    url = "https://iam-pub.cn-southwest-6034.chongqingxc02.huaweigovcloud.com/v3/auth/tokens"
    payload = {
        "auth": {
            "identity": {
                "methods": ["password"],
                "password": {
                    "user": {
                        "name": "jlpqsthjj_2_ModelArts",
                        "password": "S2nW2MKD@Huawei123",
                        "domain": {"name": "jlpqsthjj_2"}
                    }
                }
            },
            "scope": {"project": {"name": "cn-southwest-6034"}}
        }
    }
    rst = requests.post(url, headers={"Content-Type": "application/json"}, data=json.dumps(payload), verify=False)
    assert rst.status_code < 400, f"获取 Token 失败，状态码：{rst.status_code}"
    return rst.headers["X-Subject-Token"]
print(get_llm_token())
') && curl -k \
  -X POST "https://172.18.51.30:8001/v1/infers/0c7c827e-132d-49c9-809a-c817685500ef/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "X-Auth-Token: $TOKEN" \
  -d '{
    "model": "/home/<USER>/AscendCloud/Qwen2.5-14B-Instruct-LoRA-AWQ-w4g128",
    "messages": [{"role": "user", "content": "你好"}],
    "temperature": 0.7
  }'
```