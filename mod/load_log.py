# -*- coding: utf-8 -*-
"""
@File  : load_log.py
@Author: <PERSON>
@Date  : 2025/2/25 10:36
@Desc  : 
"""
import os
import warnings
import logging.config

from mod.load_and_save import load_yaml


def load_conf_file(path=None, default='default.yml'):
    if path is None or not os.path.exists(path):
        warnings.warn(f"""没有配置文件{'' if path is None else ':'+path}，采用默认配置{default}.""")
        path = os.path.join(proj_dir, 'config', default)
    conf = load_yaml(path)
    return conf


proj_dir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))


def load_log_conf(_log_conf):

    if 'path' not in _log_conf:
        _log_conf['path'] = os.path.join(proj_dir, 'data', 'logs')


    for handler in _log_conf['handlers'].values():
        if 'filename' in handler:
            handler['filename'] = os.path.join(proj_dir, 'data', 'logs', handler['filename'])
            dir_ = os.path.dirname(handler['filename'])
            os.makedirs(dir_, exist_ok=True)

    logging.config.dictConfig(_log_conf)
    _logger = logging.getLogger('root')
    _logger.info('项目根目录绝对路径: '+str(proj_dir))
    return _logger


