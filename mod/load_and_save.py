# -*- coding: utf-8 -*-
"""
@File  : load.py
@Author: <PERSON>
@Date  : 2022/8/31 17:51
@Desc  : 
"""
import yaml
import json


def load_yaml(path):
    with open(path, 'r', encoding='utf8') as f:
        data = yaml.load(f, Loader=yaml.FullLoader)
    return data


def save_yaml(data, path):
    with open(path, "w", encoding="utf-8") as yaml_file:
        yaml.dump(data, yaml_file, default_flow_style=False, allow_unicode=True, indent=4)


def load_json(path, encoding='utf8'):
    with open(path, 'r', encoding=encoding) as f:
        data = json.loads(f.read())
    return data


def save_json(path, data):
    with open(path, 'w') as f:
        f.write(json.dumps(data, ensure_ascii=False, indent=4))
    return


if __name__ == '__main__':
    a = {1: '23', 'a': 'qwr'}
    data = json.dumps(a)
    print(data)
