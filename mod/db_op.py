# -*- coding: utf-8 -*-
"""
@File  : db_op.py
@Author: <PERSON>
@Date  : 2025/2/25 15:13
@Desc  : 
"""
import pandas as pd
import psycopg2

class DB:

    def __init__(self, host, port, dbname, user, password):
        self.conn, self.cursor = self.connect(host, port, dbname, user, password)

    def connect(self, host, port, dbname, user, password):
        conn = psycopg2.connect(host=host, port=port, dbname=dbname, user=user, password=password)
        cursor = conn.cursor()
        return conn, cursor

    def query_table(self, tbl_name, columns=None):
        if columns:
            sql = f"SELECT {', '.join(columns)} FROM {tbl_name};"
        else:
            sql = f"SELECT * FROM {tbl_name};"
        data = self.query_sql(sql)
        return data

    def query_sql(self, sql):
        self.cursor.execute(sql)
        rows = self.cursor.fetchall()
        cols = [desc[0] for desc in self.cursor.description]
        data = pd.DataFrame(rows, columns=cols)
        return data

    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()



