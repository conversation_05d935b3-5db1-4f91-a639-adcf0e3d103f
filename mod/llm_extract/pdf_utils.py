# -*- coding: utf-8 -*-
"""
Created on 2024/10/11 上午11:28

@Project -> File: srv-pollution-jiulongpo-emission -> pdf_utils.py

@Author: ch<PERSON><PERSON>

@Describe:
"""
import fitz  # PyMuPDF


def add_text_watermark(pdf_path, output_path, watermark_text,
                       font_path = '/usr/share/fonts/vista/YaHeiConsolas.ttf',
                       font_size = 60):
    """为pdf添加水印"""
    # 打开现有的PDF文件
    doc = fitz.open(pdf_path)

    # 遍历PDF的每一页
    for page_num in range(len(doc)):
        page = doc[page_num]
        # 获取页面尺寸
        page_width = page.rect.width
        page_height = page.rect.height
        pos = (page_width//6, page_height//2)

        # 添加文本水印
        page.insert_text(pos, watermark_text, fontfile=fitz.Font(fontfile=font_path),
                         fontsize=font_size, rotate=0,
                         fill_opacity=0.2, overlay = True
                         )

    # 保存新的PDF文件
    doc.save(output_path)
    doc.close()


if __name__ == '__main__':
    fn = '九龙坡项目接口 V0.0.2.pdf'
    add_text_watermark(fn, fn.replace('.pdf', '-marked.pdf'), ' '.join('Rockontrol'))
