"""

  @ Author: Hawkey@rkcl

  @ Email: <EMAIL>

  @ Date: 2024-08-01 2:52:11 PM

  @ FilePath:  -> srv-pollution-jiulongpo-emission -> mod -> llm_extract -> extract_total_no_use.py

  @ Description: 

"""

# !/usr/bin/env python
# -*-coding: utf-8 -*-
import os
import requests
import pandas as pd
from io import StringIO
from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx

system_prompt = """
# Role: 信息智能提取专家

## Profile:
### 1. Take a deep breath, Let's think step by step.
### 2. ```符号内是表格名称和CSV格式的表格内容，请先转化为表格再进行分析。

## Goal:
### 根据表格内容提取污染源相关信息，并生成标准的CSV格式表格。
### 生成的表格header为：排放口名称、污染源类别、污染物名称、标准限值、单位、手工监测频次、限值对应的依据。
### CSV表格中无法查询到相关内容且为空的缺失部分置为空，用逗号隔开。

## Constraints:
### 1. 提取的内容必须严格遵循```内提供的信息，不能出现缺失或新增。
### 2. 输出必须是标准的CSV表格，CSV每一行输出的个数要和header保持一致，用逗号分割。
### 3. 只关注废水、废气、噪声、无组织排放这4种污染源类别。
### 4. 只提取手工监测的污染因子，忽略所有的有组织排放相关的表格，如有组织废水、废气等。
### 5. 标准限值为污染物对应的`执行/参照标准`限值。
### 6. 不要混合排放口名称和污染源类别，排放口名称即监测点位，污染源类别只可为废气、废水、噪声、无组织排放。
### 7. 单位为标准限值对应的单位。
### 8. 污染物名称即排放的污染物名称、也称之为污染因子、污染物、监测因子、监测项目等。
### 9. 排放口名称即污染排口等。
### 10. 限值对应的依据为参考标准限值对应的标准文件名称、标准文件依据等。
### 11. 提取噪声限值时，先查询昼间和夜间的最大噪声值分别是多少，昼间和夜间限值之间用'/'连接，如：`昼间40/夜间50`，昼间/夜间和数字之间无任何符号。
### 12. 同一个排放口排放的噪声对应的昼间和夜间值放在表格中的一行，如：`昼间40/夜间50`, 40和50分别对应昼间和夜间对应的噪声最大值。

## Example:
```csv
排放口名称,污染源类别,污染物名称,标准限值,单位,手工监测频次,限值对应的依据
厂界,废气,颗粒物,10,mg/m3,1次/年,'DB 50/418-2016'
DW001,废水,悬浮物,10,mg/m3,1次/日,污水综合排放标准GB8978-1996
DW001,废水,化学需氧量,500,Mg/L,1次/日,污水综合排放标准GB8978-1996
厂界,噪声,昼间40/夜间50,db,1次/日,《工业企业厂界环境噪声排放标准》(GB12348-2008)
```

## Initialization:
请根据输入相关表格信息:

"""

proj_dir = os.path.dirname(os.path.abspath(__file__))

os.makedirs(f'{proj_dir}/data/out', exist_ok = True)
input_dir = r'/mnt/c/Users/<USER>/Desktop/大模型/九龙坡/data/九龙坡-企业数据/processed/converted/'

for file in os.listdir(input_dir):
    path_docx = os.path.join(input_dir, file)
    out_name = os.path.basename(path_docx).replace('.docx', '.csv')

    if file in ['204.庆铃汽车股份有限公司自行监测方案.docx']:
        continue

    if os.path.exists(f'{proj_dir}/data/out/{out_name}'):
        print(f'Skip {file}')
        continue
    else:
        print(f'Begin {file}')

    try:
        txt = ""
        tables = MarkdownConverterWithPythonDocx(path_docx, 'datafame').tables('dataframe')
        for key, val in tables.items():
            txt += f">>>表格名称:{key}\n\n 表格内容:\n{val}<<<"

        a = {"model": "Qwen2-72B-Instruct-AWQ",
             "messages": [{"role": "system", "content": system_prompt},
                          {"role": "user", "content": txt},
                          ],
             "top_p": 1, "stream": False, 'presence_penalty': 0., 'temperature': 0,
             "stop_token_ids": [151643, 151644, 151645],
             "best_of": 8, "use_beam_search": True,
             #     "top_p": 0.8,  "stream": False, 'presence_penalty': 0.,
             #    "stop_token_ids": [151643,151644,151645]#   # for Qwen
             }

        res = requests.post('http://*************:8083/v1/chat/completions', json = a, stream = False)

        firm_info = pd.read_csv(
            StringIO((res.json()['choices'][0]['message']['content'].replace("```csv\n", "").replace("\n```", ""))))

        firm_info.to_csv(f'{proj_dir}/data/out/{out_name}', encoding = 'utf-8-sig', index = False)
    except Exception as e:
        print(e)
