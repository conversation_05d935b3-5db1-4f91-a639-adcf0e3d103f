# -*- coding: utf-8 -*-
"""
@File  : extract_pdf.py
@Author: <PERSON>
@Date  : 2024/9/4 14:45
@Desc  : 
"""

import fitz  # PyMuPDF
import json
import cv2
import numpy as np
import tempfile
import os
import logging
import ocrmypdf
from ocrmypdf import Verbosity
from pdfminer.high_level import extract_text
from multiprocessing import Process
from PIL import Image
from paddleocr import PaddleOCR, draw_ocr

logging.disable(logging.DEBUG)  # 关闭DEBUG日志的打印
# logging.disable(logging.WARNING)  # 关闭WARNING日志的打印


def ocrmypdf_process(path):
    """
    使用OCRMYPDF库处理PDF文件的函数。
    配置日志记录，以便跟踪处理过程。
    将输入的PDF文件转换为包含OCR（光学字符识别）数据的PDF，指定语言为中文。
    """
    # 配置日志记录，使用默认的详细级别
    ocrmypdf.configure_logging(Verbosity.default)

    # 分割文件路径和文件名
    base_path, file = os.path.split(path)

    # 使用OCRMYPDF对PDF文件进行OCR处理，指定输入输出文件路径以及语言
    ocrmypdf.ocr(os.path.join(base_path, file),
                 os.path.join(base_path,'output.pdf'),
                 language='chi_sim',
                 deskew=True,
                 force_ocr=True,
                 sidecar=os.path.join(base_path, 'output.txt'),
                 oversample=2,
                 optimize=1
                 )


def call_ocrmypdf_from_my_app(path):
    """
    从自定义应用程序中调用OCRMYPDF处理的函数。
    创建一个新的进程来运行PDF处理任务，以避免阻塞当前应用程序。
    """
    # 创建一个新进程来执行PDF处理任务
    p = Process(target=ocrmypdf_process, args=(path,))

    # 启动进程
    p.start()

    # 等待进程完成
    p.join()


def paddleocr_text_extraction(path, visualize=False, save_rst = False, rm_file = False):
    # 打开PDF文件
    doc = fitz.open(path)
    base_path, file = os.path.split(path)

    # Paddleocr目前支持的多语言语种可以通过修改lang参数进行切换
    # 例如`ch`, `en`, `fr`, `german`, `korean`, `japan`
    PAGE_NUM = len(doc)  # 将识别页码前置作为全局，防止后续打开pdf的参数和前文识别参数不一致 / Set the recognition page number
    ocr = PaddleOCR(use_angle_cls=True, lang="ch", page_num=PAGE_NUM)  # need to run only once to download and load model into memory
    # ocr = PaddleOCR(use_angle_cls=True, lang="ch", page_num=PAGE_NUM,
    #                 use_gpu=0)  # 如果需要使用GPU，请取消此行的注释 并注释上一行 / To Use GPU,uncomment this line and comment the above one.
    result = ocr.ocr(path, cls=True)
    texts = []
    for idx in range(len(result)):
        lst = []
        res = result[idx]
        if res is None:  # 识别到空页就跳过，防止程序报错 / Skip when empty result detected to avoid TypeError:NoneType
            print(f"[DEBUG] Empty page {idx + 1} detected, skip it.")
            continue
        for line in res:
            lst.append(line[1][0])
        texts.append('\n'.join(lst))

    # 显示结果
    if visualize:
        os.makedirs(os.path.join(base_path, 'result_pages'), exist_ok=True)
        imgs = []
        with fitz.open(path) as pdf:
            for pg in range(0, PAGE_NUM):
                page = pdf[pg]
                mat = fitz.Matrix(2, 2)
                pm = page.get_pixmap(matrix=mat, alpha=False)
                # if width or height > 2000 pixels, don't enlarge the image
                if pm.width > 2000 or pm.height > 2000:
                    pm = page.get_pixmap(matrix=fitz.Matrix(1, 1), alpha=False)
                img = Image.frombytes("RGB", [pm.width, pm.height], pm.samples)
                img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                imgs.append(img)
        for idx in range(len(result)):
            res = result[idx]
            if res is None:
                continue
            image = imgs[idx]
            boxes = [line[0] for line in res]
            txts = [line[1][0] for line in res]
            scores = [line[1][1] for line in res]
            im_show = draw_ocr(image, boxes, txts, scores, font_path='doc/fonts/simfang.ttf')
            im_show = Image.fromarray(im_show)
            im_show.save(os.path.join(base_path, 'result_pages', 'result_page_{}.jpg'.format(idx)))

    if save_rst:
        with open(os.path.join(base_path, 'output.json'), 'w') as f:
            json.dump(texts, f, ensure_ascii=False, indent=4)
    if rm_file:
        try:
            os.remove(path)
        except Exception as e:
            pass
    return texts


def correct_skew(image):
    """
    校正图像的倾斜

    参数:
    image: 输入的图像

    返回:
    corrected: 校正后的图像
    angle: 校正的角度
    """

    # 将图像转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 应用高斯模糊以减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 使用二值化处理
    _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # 进行边缘检测
    edges = cv2.Canny(binary, 50, 150, apertureSize=3)

    # 使用霍夫变换检测线条
    for threshold in range(50, 501, 50)[::-1]:
        lines = cv2.HoughLines(edges, 1, np.pi / 180, threshold)
        if lines is not None and len(lines) >= 5:
            break
    else:
        raise RuntimeError("图像质量较差")

    # 调试用代码，将原始图像复制到img，用于后续可视化直线检测结果
    img = image.copy()
    # 遍历检测到的每条直线
    for line in lines:
        # 获取极坐标表示的直线参数：rho和theta
        rho, theta = line[0]
        # 计算直线的cos和sin值，用于直线方程转换
        a = np.cos(theta)
        b = np.sin(theta)
        # 计算直线方程中x和y的截距点
        x0 = a * rho
        y0 = b * rho
        # 计算直线方程的两个点坐标，用于绘制直线
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * a)
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * a)

        # 在img上绘制直线，用于可视化
        cv2.line(img, (x1, y1), (x2, y2), (0, 0, 255), 2)

    # 将绘制了直线的图像保存，用于调试查看
    # cv2.imwrite(f'houghlines.jpg', img)

    # 初始化角度列表，用于后续计算平均角度
    angles = []
    # 检查是否检测到了直线，避免空指针错误
    if lines is not None:
        # 遍历所有检测到的直线，转换theta角度到度数，并调整范围
        for rho, theta in lines[:, 0]:
            angle = (theta * 180 / np.pi) - 90
            angles.append(angle)

    # 将角度列表转换为numpy数组，便于后续处理
    angles = np.array(angles)
    # 筛选角度范围在-45到45度之间的直线，以排除大部分噪声
    angles = angles[np.logical_and(-45 < angles, angles < 45)]
    # 计算角度的均值和标准差，用于后续的统计分析
    mean, std = np.mean(angles), np.std(angles)
    # 计算均值加减三倍标准差的范围，进一步筛选角度
    _min, _max = mean - 3*std, mean + 3*std
    angles = angles[np.logical_and(_min <= angles, angles <= _max)]
    # 输出最终角度列表的平均值，用于调试查看
    # print(np.mean(angles))

    # 检查是否还有角度在筛选后列表中
    if len(angles) > 0:
        # 取平均角度，作为最终的旋转角度
        angle = np.mean(angles)
        # 获取图像的宽高
        (h, w) = image.shape[:2]
        # 计算图像的中心点，作为旋转中心
        center = (w // 2, h // 2)
        # 获取旋转矩阵
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        # 执行图像旋转操作，进行图像校正
        corrected = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
        # 返回校正后的图像和旋转角度
        return corrected, angle

    return image, 0


def preprocess_pdf(bytes_data, output_pdf, dpi=300):
    """
    处理PDF文件，包括提取页面为高分辨率图像、纠正图像倾斜并保存到新的PDF文件中。

    参数:
    input_pdf: str, 输入的PDF文件路径。
    output_pdf: str, 输出处理后的PDF文件路径。
    dpi: int, 图像的分辨率，默认为300。
    """
    # 打开输入的PDF文档
    doc = fitz.Document(stream=bytes_data, filetype='pdf')
    # 创建一个新的PDF文档用于保存处理后的页面
    output_doc = fitz.open()

    # 获取原始页面尺寸，这里只取第一页的尺寸作为参考
    original_page = doc[0]
    width = original_page.rect.width
    height = original_page.rect.height

    for page_num in range(len(doc)):
        # 提取页面为高分辨率图像
        pix = doc[page_num].get_pixmap(matrix=fitz.Matrix(dpi / 72, dpi / 72))
        # 将图像数据转换为NumPy数组格式
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)

        # 纠正倾斜
        corrected_image, angle = correct_skew(img)

        # 如果纠正后的角度大于1.5度，则需要重新生成PDF页面
        if abs(angle) >= 0:
            # 将纠正后的图像保存为临时文件
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_file:
                # 无压缩保存图像到临时文件
                cv2.imwrite(temp_file.name, corrected_image, [int(cv2.IMWRITE_PNG_COMPRESSION), 0])
                # 从临时文件读取图像并添加到新的PDF页面中
                # img_pdf = fitz.open(temp_file.name)
                page = output_doc.new_page(width=width, height=height)
                page.insert_image(page.rect, filename=temp_file.name)
            os.unlink(temp_file.name)
        else:
            # 如果角度不需要纠正，则直接将原PDF页面添加到新的PDF中
            output_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)

    # 保存处理后的PDF文档
    output_doc.save(output_pdf)
    # 关闭文档
    output_doc.close()
    doc.close()


if __name__ == '__main__':
    base_path = r'E:\project\large_model\srv-pollution-jiulongpo-emission\材料'

    preprocess_pdf(os.path.join(base_path, 'input.pdf'),
                   os.path.join(base_path, 'input-rotate.pdf'),
                   dpi=500)

    # call_ocrmypdf_from_my_app(os.path.join(base_path, "tmp.pdf"))
    # text = extract_text(os.path.join(base_path, "output.pdf"))
    # text = paddleocr_process(os.path.join(base_path, "tmp.pdf"), visualize=True)
    # print(text)
