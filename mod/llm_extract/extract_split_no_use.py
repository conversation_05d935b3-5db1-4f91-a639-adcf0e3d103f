"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2024-08-06 2:59:03 PM

  @ FilePath:  -> srv-pollution-jiulongpo-emission -> mod -> llm_extract -> extract_split_no_use.py

  @ Description: 

"""

# !/usr/bin/env python
# -*-coding: utf-8 -*-


import os
import re
import json
import copy
import requests
import pandas as pd
from io import StringIO
from typing import List, Tuple, Literal, Dict
from mod.llm_extract.markdown_converter import MarkdownConverterWithPythonDocx

proj_dir = os.path.dirname(os.path.abspath(__file__))
BASE_SYTEM_PROMPT = \
    "你是一个表格信息智能提取专家,可以从多个表格文件中提取问题相关的信息。\n"\
    "符号>>><<<内为表格名称和表格内容。对于csv格式的表格, 先转化为表格,再回答问题。\n"\
    "注意个别表格可能会存在2行以上的表头，仔细分析后在回答。\n"\
    "Take a deep breath, let's think step by step, and then answer the following question. "


def request_model_qwen2_72b(system_prompt: str, user_prompt: List[Tuple[str, str]],
                            stream: bool = False, best_of: int = 4,
                            use_beam_search: bool = True,
                            optim = False, result_eval: bool = True,
                            **kwargs):
    BASE_URL = 'http://*************:8083'
    messages = [{"role": "system", "content": system_prompt}]
    for (user, assistant) in user_prompt:
        if assistant.strip() != '':
            messages.append({"role": "user", "content": user})
            messages.append({"role": "assistant", "content": assistant})
        else:
            messages.append({"role": "user", "content": user})
            break

    if use_beam_search:
        parameter = {
            "model": "Qwen2-72B-Instruct-AWQ",
            "messages": messages,
            "top_p": 1,
            "stream": stream,
            'presence_penalty': 0.,
            'temperature': 0,
            "stop_token_ids": [151643, 151644, 151645],
            "best_of": best_of,
            "use_beam_search": use_beam_search,
        }
    else:
        parameter = {
            "model": "Qwen2-72B-Instruct-AWQ",
            "messages": messages,
            "stream": stream,
            'presence_penalty': 0.,
            'temperature': 0.8,
            "stop_token_ids": [151643, 151644, 151645],
            **kwargs
        }

    if optim:
        # 多次调用大模型，然后让大模型从多次返回结果中提取最优的结果

        out = []
        for i in range(best_of):
            res = requests.post(f'{BASE_URL}/v1/chat/completions', json = parameter, stream = False)
            out.append(f'Answer {i + 1}: ' + get_llm_output(res, stream = stream))

        txt = f"""
从以下回答中提取最优的结果, 只需要回复答案结果即可:
Question: {user_prompt[-1][0]}

{out}
"""
        messages_new = [{"role": "system", "content": '你是一个最优化选择专家，善于从多个答案中选取最佳答案'},
                        {"role": "user", "content": txt}]
        parameter = {**parameter, **{"messages": messages_new}}

    res = requests.post(f'{BASE_URL}/v1/chat/completions', json = parameter, stream = stream)

    if result_eval:
        parameter['messages'].append({"role": "assistant", "content": get_llm_output(res, stream = stream)})
        txt = '根据背景信息，仔细重新评估以上回答，只给出最终答案。答案内容和以上回答保持一致, 不返回任何分析结果。'
        parameter['messages'].append({"role": "user", "content": txt})
        res = requests.post(f'{BASE_URL}/v1/chat/completions', json = parameter, stream = stream)

    return get_llm_output(res, stream = stream)


def get_llm_output(res, stream: bool = False):
    if stream:
        out = ''
        for _ in res.iter_content(None):
            if "content" not in _.decode('utf-8'):
                continue
            pat = re.compile(" ({.*})")
            a = pat.findall(_.decode('utf-8'))[0]
            b = json.loads(a)['choices'][0]['delta']
            if 'content' in b:
                out += b['content'].replace('<eoa>', '')
            else:
                continue
    else:
        out = res.json()['choices'][0]['message']['content']
    return out


def get_table_title(table_info: Dict[str, pd.DataFrame], title_prompt: str):
    txt = "所有的表格名称如下, 分别为:\n"
    for key, val in table_info.items():
        txt += f"\n表格名称:{key}, 表格前两行内容为：{val.iloc[:2].to_csv(encoding = 'utf-8-sig', index = False, header = 0)}\n"

    txt = f"{title_prompt}\n{txt}"
    titles = request_model_qwen2_72b('你是一个最优化专家，擅长从多个选择中选取最佳的答案', [(txt, '')])
    titles = titles.split(',')
    print(f'{"-" * 100}\nQuestion: {txt}\n\nAnswer:\n {titles}\n{"-" * 100}\n')

    titles = [''.join(re.findall(r'\w+', _)) for _ in titles]
    table_info_copy = copy.deepcopy(table_info)
    table_info_copy = {''.join(re.findall(r'\w+', key)): val for key, val in table_info_copy.items()}
    return titles, table_info_copy


def get_table_info(table_info: Dict[str, pd.DataFrame], titles: list, content_prompt: str, update: bool = False):
    # 一个表格一个表格的更新
    if update:
        history = []
        for title in titles:
            if len(title.strip()) == 0:
                continue
            if title not in table_info.keys():
                print(f'get_table_info Warning: {title} not in {table_info.keys()}')
                continue
            content = f">>\n>表格名称:{title}\n\n 表格内容:\n{table_info[title].to_csv(encoding = 'utf-8-sig', index = False, header = 0)}\n<<<\n\n"

            history = [[f'{content_prompt}\n{content}', '']]
            firm_info = request_model_qwen2_72b(BASE_SYTEM_PROMPT, history)
            print(print(f'{"-" * 100}\nQuestion: {content_prompt}\n{content}\n\nAnswer:\n {firm_info}\n{"-" * 100}\n'))
            try:
                tmp_firm_pd = parse_llm_csv_to_pd(firm_info)
                history[-1][1] = firm_info
            except Exception as e:
                print(f'使用每个表格update时失败: {e}')

    else:
        content = ''
        for title in titles:
            if len(title.strip()) == 0:
                continue
            if title not in table_info.keys():
                print(f'get_table_info Warning: {title} not in {table_info.keys()}')
                continue
            content += f">>>\n表格名称:{title}\n\n表格内容:\n{table_info[title].to_csv(encoding = 'utf-8-sig', index = False, header = 0)} \n<<<\n"

        if content == '':
            raise ValueError('提取信息出错, 没有找到问题相关表格title!\n')

        firm_info = request_model_qwen2_72b(BASE_SYTEM_PROMPT, [(f'{content_prompt}\n{content}', '')])
        print(f'{"-" * 100}\nQuestion: {content_prompt}\n{content}\n\nAnswer:\n {firm_info}\n{"-" * 100}\n')

    firm_info_pd = parse_llm_csv_to_pd(firm_info)

    return titles, firm_info_pd


def parse_llm_csv_to_pd(txt: str):
    csv_txt = re.compile("```csv(.*)```", re.DOTALL).findall(txt)
    if len(csv_txt) == 0:
        raise ValueError('表格信息中没有相关的内容content!\n')

    csv_pd = pd.read_csv(StringIO(csv_txt[0]))
    return csv_pd


def get_minitor_info(tables: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    title_prompt = """
在以下哪几个表格中可以提取`污染源类别、污染源名称、监测点位、手工监测指标、监测频次`等信息? 
只需要回复表格名称即可, 多个表格用逗号分隔。
"""
    titles, tables_copy = get_table_title(tables, title_prompt)

    content_prompt = """
请根据如下表格内容提取`污染源类别、污染源名称、监测点位、手工监测指标、监测频次`等信息, 
**只提取手工监测、无组织监测部分，有组织监测不需要提取**。
如果一个监测指标中有多项，需要分行输出。
如果一个监测点位中有多项，需要分行输出。
最终输出为完整的csv格式, 输出的csv格式以```csv标识开始, ```标识结束。

表格内容如下：
"""

    monitor_info = get_table_info(tables_copy, titles, content_prompt, update = False)[1]
    return monitor_info


def get_threshold_info(tables: Dict[str, pd.DataFrame], table_update: pd.DataFrame):
    out = None
    for name, var in zip(table_update['污染源名称'].to_list(), table_update['手工监测指标'].to_list()):
        title_prompt = f"""
在以下哪个表格中可以提取污染源名称: `{name}`和手工监测指标: `{var}`对应的排放限值、标准、单位、依据等信息? 
**只提取手工监测、无组织监测部分，有组织监测不需要提取**。
只需要回复表格名称即可, 多个表格用逗号分隔。
"""
        titles, tables_copy = get_table_title(tables, title_prompt)

        content_prompt = f"""
从以下>>><<<中的csv表格中查询污染物排放限值、单位、依据标准等信息, 并输出为完整的csv格式, 以```csv标识开始, ```标识结束。
输出的列名统一为:污染源名称、手工监测指标、限值、单位、依据。
`限值`列和`单位`必须与污染源名称: `{name}`和手工监测指标: `{var}`对应。
`依据`只需要包含对应德标准名称即可。
一般`限值`列和`单位`列分为在执行/参照标准限值下, 可能会存在2行至多行表头。
仔细对比每个限值，可能存在`手工监测指标`的名称有些许差异，但是实际代表同一个含义，也需要匹配。
噪声会存在夜间和昼间的限值，先查询昼间和夜间的值分别是多少, 后按`昼间限值/夜间限值`格式进行输出, 如: `昼间40/夜间50`。
对于查询不到的内容, 输出结果为空。

表格内容如下：
"""

        threshold_info = get_table_info(tables_copy, titles, content_prompt, update = False)[1]

        # out = pd.merge(table_update, threshold_info, on=['污染源名称', '手工监测指标'], how='left')
        out = threshold_info if out is None else pd.concat((out, threshold_info), axis = 0, ignore_index = True)

    return out


if __name__ == "__main__":

    os.makedirs(f'{proj_dir}/data/out', exist_ok = True)
    input_dir = r'/mnt/c/Users/<USER>/Desktop/大模型/九龙坡/data/九龙坡-企业数据/processed/converted'

    for file in os.listdir(input_dir):
        path_docx = os.path.join(input_dir, file)

        tables = MarkdownConverterWithPythonDocx(path_docx, 'dataframe')

        monitor_info = get_minitor_info(tables)

        monitor_info = monitor_info.fillna('')
        # DataFrame增加两列column

        threshold_info = get_threshold_info(tables, monitor_info)

        firm_info = pd.merge(monitor_info, threshold_info, on = ['污染源名称', '手工监测指标'], how = 'left')

        out_name = os.path.basename(path_docx).replace('.docx', '.csv')
        firm_info.to_csv(f'{proj_dir}/data/out/{out_name}', encoding = 'utf-8-sig', index = False)
