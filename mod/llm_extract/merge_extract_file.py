"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2024-08-06 3:02:34 PM

  @ FilePath:  -> srv-pollution-jiulongpo-emission -> mod -> llm_extract -> merge_extract_file.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-

import os
import pandas as pd


def merge_csv_files(file_dir_in: str):
    """
    merge csv files in a directory
    """
    files = os.listdir(file_dir_in)

    files = sorted(files, key=lambda num: int(num.split('.')[0]))

    out = pd.DataFrame()

    for file in files:
        # print(file)
        df = pd.read_csv(f'{file_dir_in}/{file}', header=0, sep=',')
        df['file_name'] = file.replace('.csv', '')
        df['id'] = int(file.split('.')[0])
        out = pd.concat([out, df], ignore_index=True, axis=0)

    return out


if __name__ == "__main__":

    import sys
    sys.path.append('../../')

    from lib import proj_dir

    file_dir = f'{proj_dir}/data/firm_extract'

    out = merge_csv_files(file_dir)
    
    firm_info = pd.read_excel(f'{proj_dir}/data/base_firm_info/企业名称.xlsx', header=0)

    out = pd.merge(out, firm_info, on='id', how='left')
    print(out)
    # out.to_excel('merge.xlsx', index=False)