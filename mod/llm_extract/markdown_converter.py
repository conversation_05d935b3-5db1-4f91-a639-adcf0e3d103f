"""

  @ Author: <PERSON><PERSON>@rkcl

  @ Email: <EMAIL>

  @ Date: 2024-07-31 5:15:14 PM

  @ FilePath:  -> srv-pollution-jiulongpo-emission -> mod -> llm_extract -> markdown_converter.py

  @ Description: 

"""

#!/usr/bin/env python
#-*-coding: utf-8 -*-


import os
import re
import pandas as pd

# 基于python-docx库进行转换
from docx import Document
from docx.oxml import CT_P, CT_Tbl

# 基于pypandoc库进行转换
import pypandoc


class MarkdownConverterWithPythonDocx:
    """docx文档转markdown"""
    def __init__(self, docx_path, *args, **kwargs):
        self.docx_path = docx_path
        self.doc = Document(self.docx_path)

    def to_markdown(self, out_path: str = None):
        """将docx文档中的所有信息转成markdown"""

        markdown_lines = []
        tables = self.doc.tables

        # Iterate over all elements in the document
        for element in self.doc.element.body:
            
            if isinstance(element, CT_P):
                text = element.text.strip()
                if text:
                    markdown_lines.append(text)
            elif isinstance(element, CT_Tbl):

                # 获取表格对象, 每次弹出一个表格
                markdown_lines.append(self._table_extract(tables.pop(0)))

        if out_path:
            with open(out_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_lines))
        else:        
            return '\n'.join(markdown_lines)

    def _table_extract(self, table):
        markdown_table = '<table>\n'
                # 遍历表格的所有行
                
        for row in table.rows:
            # 初始化当前行的Markdown字符串
            markdown_table += '<tr>\n'

            # 计算每个单元格重复的rowspan个数
            row_val, row_cnt = remove_consecutive_duplicates([cell.text for cell in row.cells])
            
            # 将当前行的Markdown字符串添加到Markdown表格列表 
            for val, cnt in zip(row_val, row_cnt):
                markdown_table += f'<td colspan="{cnt}">{val}</td>\n' if cnt != 1 else f'<td>{val}</td>\n'

            markdown_table += '</tr>\n'

        markdown_table += '</table>\n'

        return markdown_table

    def tables(self, format_: str = 'markdown'):
        """提取表格中的docx中的表格信息, 不提取文档相关信息, 问题: 网格拆分, 忽略"""

        assert format_ in ['markdown', 'csv', 'dataframe'], 'format must be markdown, csv or dataframe'
        
        # 初始化结果列表
        tables_data = {}
        
        for table in self.doc.tables:
            table_title = None
            # 假设标题在表格前面的一段文字中
            if table._element.getprevious() is not None:
                table_title = table._element.getprevious().text
            
            # 读取表格内容
            data = []
            for row in table.rows:
                row_data = [cell.text for cell in row.cells]
                data.append(row_data)
            
            # 将表格内容转换为 DataFrame
            df = pd.DataFrame(data)
            
            if format_ == 'dataframe':
                # 存储标题和 DataFrame
                tables_data.update({re.sub(r'\s+', ' ', table_title).strip(): df})
            
            if format_ == 'markdown':
                # 存储标题和 Markdown 表格
                tables_data.update({re.sub(r'\s+', ' ', table_title).strip(): self._table_extract(table)})

            if format_ == 'csv':
                tables_data.update({re.sub(r'\s+', ' ', table_title).strip():
                                    df.to_csv(header=0, index=False, encoding='utf-8-sig', sep=',')})
        
        return tables_data


def remove_consecutive_duplicates(lst):
    # 删除重复元素，并统计重复个数[1 2 2 1 1 3 3] -> [1 2 1 3], 重复个数为 [1 2 2 2]
    lst_out = [lst[i] for i in range(len(lst)) if i == 0 or lst[i] != lst[i - 1]]

    lst_num = [0] * len(lst_out)

    istr = 0
    for key, val in enumerate(lst_out):

        for i in range(istr, len(lst)):
            if lst[i] == val:
                lst_num[key] += 1
            else:
                istr = i
                break
        
    return lst_out, lst_num


class MarkdownConverterWithPypandoc:
    """docx文档转markdown"""
    def __init__(self, docx_path):
        self.docx_path = docx_path

    def to_markdown(self, out_path: str = None):
        """将docx文档中的所有信息转成markdown"""
        # 查看所有支持格式 pypandoc.get_pandoc_formats()[1]
        content = pypandoc.convert_file(self.docx_path, to='markdown_strict', outputfile=out_path)
        return content
    

if __name__ == "__main__":

    dir_path = '/mnt/c/Users/<USER>/Desktop/大模型/九龙坡/data/九龙坡-企业数据/processed/converted/'

    for file_path in os.listdir(dir_path):
        if not file_path.endswith('.docx'):
            continue

        docx = MarkdownConverterWithPypandoc(f"{dir_path}/{file_path}")
        docx.to_markdown(f'/mnt/c/Users/<USER>/Desktop/{file_path.replace(".docx", ".md")}')
        
        import sys
        sys.exit()
