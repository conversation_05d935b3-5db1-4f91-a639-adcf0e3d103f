# -*- coding: utf-8 -*-
"""
@File  : s3_operate.py
@Author: <PERSON>
@Date  : 2025/6/3 11:12
@Desc  : 
"""
import os
import boto3
from botocore.client import Config
from botocore.exceptions import ClientError
from tqdm import tqdm
import time
import sys

# S3配置
ENDPOINT_URL = "https://object.rockontrol.com"
ACCESS_KEY = "5PT6CG54EONCTSF12L9Q"
SECRET_KEY = "2DXIdbryIgZsC0geVaBjFWQPT2WUBA9YumVxBSms"
BUCKET_NAME = "new-bucket-env"
S3_DIRECTORY = "排污许可证-原始文件-20250512/青海省/西宁市/"
LOCAL_DIR = "/home/<USER>/project/large_model/材料/西宁"


def print_info(message):
    """打印信息级别消息"""
    print(f"[INFO] {message}")


def print_warning(message):
    """打印警告级别消息"""
    print(f"[WARNING] {message}")


def print_error(message):
    """打印错误级别消息"""
    print(f"[ERROR] {message}")


def create_s3_client():
    """创建并返回S3客户端"""
    return boto3.client(
        's3',
        endpoint_url=ENDPOINT_URL,
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=SECRET_KEY,
        config=Config(
            signature_version='s3v4',
            connect_timeout=120,
            read_timeout=300,
            retries={'max_attempts': 10}
        )
    )


def verify_bucket_access(s3_client):
    """验证存储桶访问权限"""
    try:
        s3_client.head_bucket(Bucket=BUCKET_NAME)
        print_info(f"✅ 成功访问存储桶: {BUCKET_NAME}")
        return True
    except ClientError as e:
        error_code = e.response['Error']['Code']
        print_error(f"❌ 存储桶访问错误: {error_code} - {e.response['Error']['Message']}")
        if error_code == '403':
            print_error("可能原因: 访问密钥无权限或已过期")
        elif error_code == '404':
            print_error("可能原因: 存储桶不存在")
        return False
    except Exception as e:
        print_error(f"❌ 未知错误: {str(e)}")
        return False


def get_all_objects(s3_client):
    """使用分页器获取所有对象（解决ContinuationToken问题）"""
    all_objects = []
    total_objects = 0
    page_count = 0

    print_info(f"🔍 开始获取目录 '{S3_DIRECTORY}' 下的所有对象...")

    try:
        # 创建分页器
        paginator = s3_client.get_paginator('list_objects_v2')

        # 配置分页参数
        page_iterator = paginator.paginate(
            Bucket=BUCKET_NAME,
            Prefix=S3_DIRECTORY,
            PaginationConfig={'PageSize': 2000, 'MaxItems': 2000}
        )

        # 遍历所有页面
        for page in page_iterator:
            page_count += 1

            # 检查是否有内容
            if 'Contents' in page:
                objects_in_page = page['Contents']
                all_objects.extend(objects_in_page)
                total_objects += len(objects_in_page)
                print_info(f"📄 第 {page_count} 页获取到 {len(objects_in_page)} 个对象")

                # 调试信息
                print(f"[DEBUG] IsTruncated: {page.get('IsTruncated', False)}")
                print(f"[DEBUG] NextContinuationToken: {page.get('NextContinuationToken', 'None')}")
                print(f"[DEBUG] KeyCount: {page.get('KeyCount', 0)}")
            else:
                print_warning(f"⚠️ 第 {page_count} 页没有找到 'Contents' 键")
                print(f"[DEBUG] 完整响应: {page}")

        print_info(f"✅ 获取完成! 共 {page_count} 页, 总计 {total_objects} 个对象")
        return all_objects

    except ClientError as e:
        error_code = e.response['Error']['Code']
        print_error(f"❌ 列表对象错误: {error_code} - {e.response['Error']['Message']}")
        return []
    except Exception as e:
        print_error(f"❌ 获取对象列表时出错: {str(e)}")
        return []


def download_objects(s3_client, all_objects):
    """下载所有对象到本地"""
    if not all_objects:
        print_error("❌ 没有对象可下载")
        return False

    success_count = 0
    skip_count = 0
    fail_count = 0

    print_info(f"⬇️ 开始下载 {len(all_objects)} 个文件...")

    # 创建进度条
    progress_bar = tqdm(total=len(all_objects), desc="下载进度", unit="文件", file=sys.stdout)

    for obj in all_objects:
        key = obj['Key']

        # 跳过目录标记对象
        if key.endswith('/'):
            skip_count += 1
            progress_bar.update(1)
            continue

        # 构建本地路径
        relative_path = os.path.relpath(key, S3_DIRECTORY)
        local_path = os.path.join(LOCAL_DIR, relative_path)
        local_dir = os.path.dirname(local_path)

        # 创建本地目录
        os.makedirs(local_dir, exist_ok=True)

        try:
            # 下载文件（添加重试机制）
            for attempt in range(3):
                try:
                    # 添加超时控制
                    s3_client.download_file(
                        Bucket=BUCKET_NAME,
                        Key=key,
                        Filename=local_path,
                        ExtraArgs={'RequestPayer': 'requester'}  # 某些存储需要这个参数
                    )
                    success_count += 1
                    # 缩短文件名显示
                    short_name = os.path.basename(key)
                    if len(short_name) > 30:
                        short_name = short_name[:15] + "..." + short_name[-15:]
                    progress_bar.set_postfix_str(f"{short_name}")
                    break
                except Exception as e:
                    if attempt < 2:
                        wait_time = (attempt + 1) * 5  # 增加等待时间
                        print_warning(f"⚠️ 下载失败 (尝试 {attempt + 1}/3): {key} - {str(e)}")
                        print_warning(f"⏳ 等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        print_error(f"❌ 下载失败: {key} - {str(e)}")
                        fail_count += 1
        except Exception as e:
            print_error(f"❌ 下载失败: {key} - {str(e)}")
            fail_count += 1

        progress_bar.update(1)

    progress_bar.close()

    print("\n📊 下载统计:")
    print(f"✅ 成功: {success_count}")
    print(f"⏭️ 跳过目录标记: {skip_count}")
    print(f"❌ 失败: {fail_count}")

    return success_count > 0


def main():
    start_time = time.time()
    print("=" * 70)
    print("🚀 开始S3目录下载任务")
    print(f"🔗 Endpoint: {ENDPOINT_URL}")
    print(f"📦 存储桶: {BUCKET_NAME}")
    print(f"📂 源目录: s3://{BUCKET_NAME}/{S3_DIRECTORY}")
    print(f"💾 目标目录: {LOCAL_DIR}")
    print("=" * 70)

    s3_client = create_s3_client()

    if not verify_bucket_access(s3_client):
        return

    # 获取所有对象
    print_info("🔄 正在获取对象列表...")
    all_objects = get_all_objects(s3_client)

    if not all_objects:
        print_error("❌ 未找到可下载的对象，任务终止")
        return

    # 下载所有对象
    if download_objects(s3_client, all_objects):
        elapsed = time.time() - start_time
        minutes, seconds = divmod(elapsed, 60)
        print("\n" + "=" * 70)
        print(f"🎉 任务成功完成! 总耗时: {int(minutes)}分{seconds:.2f}秒")
        print("=" * 70)
    else:
        print("\n" + "=" * 70)
        print_error("❌ 任务失败")
        print("=" * 70)


if __name__ == "__main__":
    main()